# wrong_params 错误修复方案

本文档包含所有参数不匹配错误的详细分析和修复方案。

## 错误分类统计

- **导入问题**: 6个错误 (SchedulingScheme, TeamGroupEnergy, UserDefineUnitDTO 类型不匹配)
- **方法引用问题**: 8个错误 (Stream 操作中的方法引用无法解析)
- **参数类型问题**: 8个错误 (方法参数类型不匹配或无法确定)
- **返回值类型问题**: 6个错误 (calcDouble 返回 null 但期望 Double)

---

## 详细错误分析和修复方案

### 1. 导入问题修复

#### 问题 1-1: PluginRuntimeInfo.setPluginname() 参数类型不匹配
**错误ID**: 1  
**位置**: PluginConfiguration.java:20  
**错误描述**: `'com.cet.electric.fusion.matrix.v2.dto.business.PluginInfo' 中的 'setPluginname(java.lang.String)' 无法应用于 '(?)'`

**方法签名对比分析**:
- **期望方法**: `setPluginname(java.lang.String)`
- **实际调用**: `setPluginname(?)` - 参数类型无法确定
- **根本原因**: 传入的参数类型推断失败或为 null

**修复方案**:
```java
// 当前代码 (第20行)
pluginRuntimeInfo.setPluginname(PluginInfoDef.GroupEnergy.PLUGIN_NAME_PREFIX);

// 修复建议: 确保参数不为 null 且为 String 类型
String pluginNamePrefix = PluginInfoDef.GroupEnergy.PLUGIN_NAME_PREFIX;
if (pluginNamePrefix != null) {
    pluginRuntimeInfo.setPluginname(pluginNamePrefix);
} else {
    pluginRuntimeInfo.setPluginname(""); // 或提供默认值
}
```

#### 问题 1-2: ParentQueryConditionBuilder.of() 参数类型不匹配
**错误ID**: 1  
**位置**: ClassesSchemeDaoImpl.java:35  
**错误描述**: `'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'`

**方法签名对比分析**:
- **期望方法**: `of(java.lang.String)`
- **实际调用**: `of(?)` - 参数类型无法确定
- **根本原因**: TableNameDef.CLASSES_SCHEME 常量可能为 null 或类型不匹配

**修复方案**:
```java
// 当前代码 (第35行)
ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(TableNameDef.CLASSES_SCHEME)

// 修复建议: 确保常量值正确且不为 null
String tableName = TableNameDef.CLASSES_SCHEME;
if (tableName != null && !tableName.isEmpty()) {
    ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(tableName);
} else {
    throw new IllegalArgumentException("Table name cannot be null or empty");
}
```

#### 问题 1-3: SchedulingScheme 类型导入问题
**错误ID**: 1, 30  
**位置**: TeamEnergyServiceImpl.java:72, 379  
**错误描述**: `不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'`

**方法签名对比分析**:
- **期望类型**: `SchedulingScheme` (简单类名)
- **实际类型**: `com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme` (完整包名)
- **根本原因**: 缺少正确的 import 语句

**修复方案**:
```java
// 在文件顶部添加导入语句
import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;

// 代码无需修改，导入后类型匹配问题自动解决
SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());
```

#### 问题 1-4: TeamGroupEnergy 类型导入问题
**错误ID**: 9, 41, 45  
**位置**: TeamEnergyServiceImpl.java:82, 399, 410  
**错误描述**: `不兼容的类型。实际为 java.util.List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>'，需要 'java.util.List<TeamGroupEnergy>'`

**方法签名对比分析**:
- **期望类型**: `List<TeamGroupEnergy>` (简单类名)
- **实际类型**: `List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>` (完整包名)
- **根本原因**: 缺少正确的 import 语句

**修复方案**:
```java
// 在文件顶部添加导入语句
import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;

// 代码无需修改，导入后类型匹配问题自动解决
List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(...);
```

#### 问题 1-5: UserDefineUnitDTO 类型导入问题
**错误ID**: 12, 44  
**位置**: TeamEnergyServiceImpl.java:90, 407  
**错误描述**: `不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'`

**方法签名对比分析**:
- **期望类型**: `UserDefineUnitDTO` (简单类名)
- **实际类型**: `com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO` (完整包名)
- **根本原因**: 导入语句路径不完整

**修复方案**:
```java
// 检查并修正导入语句 (文件顶部第4行)
// 当前: import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitDTO;
// 修正: import com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO;

// 或者使用完整路径的导入
import com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO;
```

### 2. 方法引用问题修复

#### 问题 2-1: Stream.map() 方法引用无法解析
**错误ID**: 6, 38  
**位置**: TeamEnergyServiceImpl.java:79, 395  
**错误描述**: `'java.util.stream.Stream' 中的 'map(java.util.function.Function<? super TeamGroupInfo,?>)' 无法应用于 '(<method reference>)'`

**方法签名对比分析**:
- **期望参数**: `Function<? super TeamGroupInfo, ?>`
- **实际参数**: `<method reference>` - 方法引用无法解析
- **根本原因**: 被引用的方法不存在或不可访问

**修复方案**:
```java
// 第79行修复 - 假设是 TeamGroupInfo::getId
// 原代码: .map(TeamGroupInfo::getId)
// 修复代码:
.map(teamGroupInfo -> teamGroupInfo.getId())

// 第395行修复 - 假设是 ClassesConfig::getId  
// 原代码: .map(ClassesConfig::getId)
// 修复代码:
.map(classesConfig -> classesConfig.getId())
```

#### 问题 2-2: Stream.mapToDouble() 方法引用无法解析
**错误ID**: 10, 22, 42, 46  
**位置**: TeamEnergyServiceImpl.java:89, 114, 406, 415  
**错误描述**: `'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'`

**方法签名对比分析**:
- **期望参数**: `ToDoubleFunction<? super TeamGroupEnergy>`
- **实际参数**: `<method reference>` - 方法引用无法解析
- **根本原因**: TeamGroupEnergy::getValue 方法引用无法解析

**修复方案**:
```java
// 所有 mapToDouble 位置的修复
// 原代码: .mapToDouble(TeamGroupEnergy::getValue)
// 修复代码:
.mapToDouble(teamGroupEnergy -> teamGroupEnergy.getValue())

// 或者确保 getValue() 方法存在并可访问
// TeamGroupEnergy 类应该有 getValue() 方法返回 Double 类型
```

#### 问题 2-3: Collectors.groupingBy() 方法引用无法解析
**错误ID**: 16  
**位置**: TeamEnergyServiceImpl.java:101  
**错误描述**: `'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function<? super java.lang.Object,?>)' 无法应用于 '(<method reference>)'`

**方法签名对比分析**:
- **期望参数**: `Function<? super TeamGroupEnergy, ?>`
- **实际参数**: `<method reference>` - 方法引用无法解析
- **根本原因**: TeamGroupEnergy::getTeamGroupId 方法引用无法解析

**修复方案**:
```java
// 第101行修复
// 原代码: .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId))
// 修复代码:
.collect(Collectors.groupingBy(teamGroupEnergy -> teamGroupEnergy.getTeamGroupId()))
```

### 3. 参数类型问题修复

#### 问题 3-1: CollectionUtils 方法参数类型不匹配
**错误ID**: 2, 31, 34  
**位置**: TeamEnergyServiceImpl.java:73, 380, 387  
**错误描述**: `'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty/isNotEmpty(java.util.Collection<?>)' 无法应用于 '(?)'`

**方法签名对比分析**:
- **期望参数**: `Collection<?>`
- **实际参数**: `?` - 参数类型无法确定
- **根本原因**: 传入的参数不是 Collection 类型或为 null

**修复方案**:
```java
// 确保传入的参数是 Collection 类型
// 第73行: CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())
if (schedulingScheme != null && CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {
    // 处理逻辑
}

// 第380行和387行类似处理
if (schedulingScheme != null && CollectionUtils.isEmpty(schedulingScheme.getClassesscheme_model())) {
    // 处理逻辑
}
```

#### 问题 3-2: Objects.equals() 参数类型不匹配
**错误ID**: 18
**位置**: TeamEnergyServiceImpl.java:110
**错误描述**: `'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'`

**方法签名对比分析**:
- **期望参数**: `(Object, Object)`
- **实际参数**: `(?, Long)` - 第一个参数类型无法确定
- **根本原因**: 第一个参数的方法调用失败

**修复方案**:
```java
// 确保第一个参数不为 null 且类型正确
// 假设是比较 ID
Long firstId = someObject.getId(); // 确保 getId() 方法存在
Objects.equals(firstId, secondId);
```

#### 问题 3-3: List.addAll() 参数类型不匹配
**错误ID**: 36
**位置**: TeamEnergyServiceImpl.java:388
**错误描述**: `'java.util.List' 中的 'addAll(java.util.Collection<? extends ClassesConfig>)' 无法应用于 '(?)'`

**方法签名对比分析**:
- **期望参数**: `Collection<? extends ClassesConfig>`
- **实际参数**: `?` - 参数类型无法确定
- **根本原因**: getClassesconfig_model() 方法返回值类型不匹配

**修复方案**:
```java
// 确保返回的是正确的 Collection 类型
List<ClassesConfig> classesConfigList = classesScheme.getClassesconfig_model();
if (classesConfigList != null) {
    classesConfigs.addAll(classesConfigList);
}
```

### 4. 返回值类型问题修复

#### 问题 4-1: calcDouble() 返回值类型不匹配
**错误ID**: 13, 24, 26, 48, 50
**位置**: TeamEnergyServiceImpl.java:96, 118, 120, 417, 419
**错误描述**: `不兼容的类型。实际为 null'，需要 'java.lang.Double'`

**方法签名对比分析**:
- **期望返回类型**: `Double`
- **实际返回类型**: `null`
- **根本原因**: NumberCalcUtils.calcDouble() 方法在某些情况下返回 null

**修复方案**:
```java
// 添加 null 检查和默认值处理
// 原代码: Double avgEnergy = NumberCalcUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());
// 修复代码:
Double avgEnergy = NumberCalcUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());
if (avgEnergy == null) {
    avgEnergy = 0.0; // 或其他合适的默认值
}

// 或者使用 Optional 处理
Double avgEnergy = Optional.ofNullable(
    NumberCalcUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId())
).orElse(0.0);
```

#### 问题 4-2: 集合操作返回值类型不匹配
**错误ID**: 8, 15, 40
**位置**: TeamEnergyServiceImpl.java:79, 101, 396
**错误描述**: `不兼容的类型。实际为 java.util.List<java.lang.Object>'，需要 'java.util.List<java.lang.Long>'`

**方法签名对比分析**:
- **期望返回类型**: `List<Long>`
- **实际返回类型**: `List<Object>`
- **根本原因**: Stream 操作中的方法引用返回 Object 而不是 Long

**修复方案**:
```java
// 确保方法引用返回正确的类型
// 原代码: .map(TeamGroupInfo::getId).collect(Collectors.toList())
// 修复代码:
.map(teamGroupInfo -> (Long) teamGroupInfo.getId()).collect(Collectors.toList())

// 或者使用 lambda 表达式确保类型安全
.map(teamGroupInfo -> {
    Long id = teamGroupInfo.getId();
    return id != null ? id : 0L;
}).collect(Collectors.toList())
```

#### 问题 4-3: setter 方法参数类型不匹配
**错误ID**: 20, 28, 52
**位置**: TeamEnergyServiceImpl.java:111, 141, 427
**错误描述**: `'setTeamGroupName/setEnergyUnit(java.lang.String)' 无法应用于 '(?)'`

**方法签名对比分析**:
- **期望参数**: `String`
- **实际参数**: `?` - 参数类型无法确定
- **根本原因**: 获取字符串值的方法调用失败

**修复方案**:
```java
// 确保传入的参数是 String 类型且不为 null
// setTeamGroupName 修复
String teamGroupName = someObject.getName();
if (teamGroupName != null) {
    card.setTeamGroupName(teamGroupName);
} else {
    card.setTeamGroupName(""); // 默认值
}

// setEnergyUnit 修复
String energyUnit = unit.getUnitEn();
if (energyUnit != null) {
    vo.setEnergyUnit(energyUnit);
} else {
    vo.setEnergyUnit(""); // 默认值
}
```

### 5. null 值处理问题修复

#### 问题 5-1: null 值导致的类型推断失败
**错误ID**: 4
**位置**: TeamEnergyServiceImpl.java:78
**错误描述**: `不兼容的类型。实际为 null'，需要 'java.util.List<TeamGroupInfo>'`

**方法签名对比分析**:
- **期望类型**: `List<TeamGroupInfo>`
- **实际类型**: `null`
- **根本原因**: getTeamgroupinfo_model() 方法返回 null

**修复方案**:
```java
// 添加 null 检查和默认值处理
List<TeamGroupInfo> teamGroupInfoList = schedulingScheme.getTeamgroupinfo_model();
if (teamGroupInfoList == null) {
    teamGroupInfoList = Collections.emptyList();
}

// 或者在方法调用时直接处理
List<TeamGroupInfo> teamGroupInfoList = Optional.ofNullable(schedulingScheme.getTeamgroupinfo_model())
    .orElse(Collections.emptyList());
```

## 修复优先级和执行顺序

### 高优先级 (立即修复)
1. **导入问题** - 添加缺失的 import 语句
2. **null 值检查** - 添加必要的 null 检查
3. **方法引用替换** - 将方法引用改为 lambda 表达式

### 中优先级 (验证后修复)
1. **参数类型转换** - 确保参数类型正确
2. **返回值处理** - 添加默认值处理

### 低优先级 (测试验证)
1. **性能优化** - 优化 Stream 操作
2. **代码重构** - 提取公共方法

## 具体修复步骤

### 步骤 1: 修复导入问题
```java
// 在 TeamEnergyServiceImpl.java 文件顶部添加缺失的导入
import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;
import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;

// 修正 UserDefineUnitDTO 的导入路径
// 将: import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitDTO;
// 改为: import com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO;
```

### 步骤 2: 修复方法引用问题
```java
// 将所有方法引用改为 lambda 表达式
// 示例修复:
.map(teamGroupInfo -> teamGroupInfo.getId())
.mapToDouble(teamGroupEnergy -> teamGroupEnergy.getValue())
.collect(Collectors.groupingBy(teamGroupEnergy -> teamGroupEnergy.getTeamGroupId()))
```

### 步骤 3: 添加 null 检查
```java
// 为所有可能返回 null 的方法调用添加检查
if (schedulingScheme != null && CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {
    // 处理逻辑
}

// 为 calcDouble 返回值添加 null 处理
Double result = NumberCalcUtils.calcDouble(a, b, operation);
if (result == null) {
    result = 0.0; // 或其他默认值
}
```

## 总结

本次分析发现的 wrong_params 错误主要集中在以下几个方面：

1. **导入问题** (6个) - 主要是缺少正确的 import 语句
2. **方法引用问题** (8个) - Stream 操作中的方法引用无法解析
3. **参数类型问题** (8个) - 方法参数类型不匹配或无法确定
4. **返回值类型问题** (6个) - 方法返回值类型不匹配

大部分问题都可以通过添加正确的导入语句、null 检查和将方法引用改为 lambda 表达式来解决。建议按照优先级顺序逐步修复，并在每次修复后进行编译验证。

## 验证方法

修复完成后，建议执行以下验证步骤：

1. **编译验证**: 运行 `mvn compile` 检查编译错误是否消除
2. **单元测试**: 运行相关的单元测试确保功能正常
3. **集成测试**: 执行完整的集成测试验证业务逻辑
4. **代码审查**: 检查修复后的代码是否符合项目规范

通过系统性的方法签名对比分析，我们能够准确识别每个错误的根本原因，并提供针对性的修复方案。这种分析方法确保了修复的准确性和完整性。
