{"title": "方法扫描问题报告", "total_count": 197, "issues_by_class": {"PluginConfiguration": [{"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.config", "class": "PluginConfiguration", "missing_method": "(()", "description": "'com.cet.electric.fusion.matrix.v2.dto.business.PluginInfo' 中的 'setPluginname(java.lang.String)' 无法应用于 '(?)'", "line": "20"}], "ClassesSchemeDaoImpl": [{"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "class": "ClassesSchemeDaoImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'", "line": "35"}], "SchedulingSchemeDaoImpl": [{"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "class": "SchedulingSchemeDaoImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'", "line": "85"}, {"issue_id": 2, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "class": "SchedulingSchemeDaoImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'", "line": "103"}, {"issue_id": 3, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "class": "SchedulingSchemeDaoImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'", "line": "72"}, {"issue_id": 4, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "class": "SchedulingSchemeDaoImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'", "line": "47"}], "TeamConfigServiceImpl": [{"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamConfigServiceImpl", "missing_method": "queryUserBatch()", "description": "不兼容的类型。实际为 null'，需要 'com.cet.eem.fusion.common.entity.Result&lt;java.util.List&lt;com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo&gt;&gt;'", "line": "535"}, {"issue_id": 2, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamConfigServiceImpl", "missing_method": "queryUserBatch()", "description": "无法解析 'NodeAuthCheckService' 中的方法 'queryUserBatch'", "line": "535"}], "TeamEnergyServiceImpl": [{"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryAssociationNodeById()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'", "line": "72"}, {"issue_id": 2, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "73"}, {"issue_id": 3, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "73"}, {"issue_id": 4, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'", "line": "78"}, {"issue_id": 5, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "78"}, {"issue_id": 6, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "79"}, {"issue_id": 7, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId'", "line": "79"}, {"issue_id": 8, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'", "line": "79"}, {"issue_id": 9, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryTeamGroupEnergy()", "description": "不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'", "line": "82"}, {"issue_id": 10, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "89"}, {"issue_id": 11, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "89"}, {"issue_id": 12, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryUnitCoef()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'", "line": "90"}, {"issue_id": 13, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "96"}, {"issue_id": 14, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "96"}, {"issue_id": 15, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'", "line": "101"}, {"issue_id": 16, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "101"}, {"issue_id": 17, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamGroupId()", "description": "无法解析方法 'getTeamGroupId'", "line": "101"}, {"issue_id": 18, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "t()", "description": "'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'", "line": "110"}, {"issue_id": 19, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "110"}, {"issue_id": 20, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'", "line": "111"}, {"issue_id": 21, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName()'", "line": "111"}, {"issue_id": 22, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "114"}, {"issue_id": 23, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "114"}, {"issue_id": 24, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "118"}, {"issue_id": 25, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "118"}, {"issue_id": 26, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "120"}, {"issue_id": 27, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "120"}, {"issue_id": 28, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'", "line": "141"}, {"issue_id": 29, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getUnitEn()", "description": "无法解析方法 'getUnitEn()'", "line": "141"}, {"issue_id": 30, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryAssociationNodeById()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'", "line": "379"}, {"issue_id": 31, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "380"}, {"issue_id": 32, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "380"}, {"issue_id": 33, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "386"}, {"issue_id": 34, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "387"}, {"issue_id": 35, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "387"}, {"issue_id": 36, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.List' 中的 'addAll(java.util.Collection&lt;? extends ClassesConfig&gt;)' 无法应用于 '(?)'", "line": "388"}, {"issue_id": 37, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "388"}, {"issue_id": 38, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "395"}, {"issue_id": 39, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId'", "line": "395"}, {"issue_id": 40, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'", "line": "396"}, {"issue_id": 41, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryClassesConfigDayEnergy()", "description": "不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'", "line": "399"}, {"issue_id": 42, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "406"}, {"issue_id": 43, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "406"}, {"issue_id": 44, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryUnitCoef()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'", "line": "407"}, {"issue_id": 45, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryClassesConfigDayEnergy()", "description": "不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'", "line": "410"}, {"issue_id": 46, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "415"}, {"issue_id": 47, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "415"}, {"issue_id": 48, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "417"}, {"issue_id": 49, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(Double, double, int)'", "line": "417"}, {"issue_id": 50, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "419"}, {"issue_id": 51, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(Double, double, int)'", "line": "419"}, {"issue_id": 52, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'", "line": "427"}, {"issue_id": 53, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getUnitEn()", "description": "无法解析方法 'getUnitEn()'", "line": "427"}, {"issue_id": 54, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'", "line": "430"}, {"issue_id": 55, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "430"}, {"issue_id": 56, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'", "line": "433"}, {"issue_id": 57, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "433"}, {"issue_id": 58, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "434"}, {"issue_id": 59, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName'", "line": "434"}, {"issue_id": 60, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 R'，需要 'java.lang.String'", "line": "434"}, {"issue_id": 61, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'collect(java.util.stream.Collector&lt;? super java.lang.Object,A,R&gt;)' 无法应用于 '(java.util.stream.Collector&lt;java.lang.CharSequence,capture&lt;?&gt;,java.lang.String&gt;)'", "line": "434"}, {"issue_id": 62, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "440"}, {"issue_id": 63, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "441"}, {"issue_id": 64, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "441"}, {"issue_id": 65, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "444"}, {"issue_id": 66, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "stream()", "description": "无法解析方法 'stream()'", "line": "444"}, {"issue_id": 67, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "filter()", "description": "无法解析方法 'filter(&lt;lambda expression&gt;)'", "line": "445"}, {"issue_id": 68, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'", "line": "445"}, {"issue_id": 69, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "445"}, {"issue_id": 70, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 null'，需要 'java.util.List&lt;ClassesConfig&gt;'", "line": "445"}, {"issue_id": 71, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "无法解析方法 'collect(Collector&lt;T, capture of ?, List&lt;T&gt;&gt;)'", "line": "445"}, {"issue_id": 72, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO.ClassesName' 中的 'setSchemeName(java.lang.String)' 无法应用于 '(?)'", "line": "450"}, {"issue_id": 73, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName()'", "line": "450"}, {"issue_id": 74, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "451"}, {"issue_id": 75, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName'", "line": "451"}, {"issue_id": 76, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 R'，需要 'java.lang.String'", "line": "451"}, {"issue_id": 77, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'collect(java.util.stream.Collector&lt;? super java.lang.Object,A,R&gt;)' 无法应用于 '(java.util.stream.Collector&lt;java.lang.CharSequence,capture&lt;?&gt;,java.lang.String&gt;)'", "line": "451"}, {"issue_id": 78, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryAssociationNodeById()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'", "line": "163"}, {"issue_id": 79, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "164"}, {"issue_id": 80, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "164"}, {"issue_id": 81, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'", "line": "169"}, {"issue_id": 82, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "169"}, {"issue_id": 83, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "170"}, {"issue_id": 84, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId'", "line": "170"}, {"issue_id": 85, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'", "line": "170"}, {"issue_id": 86, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "173"}, {"issue_id": 87, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "173"}, {"issue_id": 88, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "174"}, {"issue_id": 89, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "stream()", "description": "无法解析方法 'stream()'", "line": "174"}, {"issue_id": 90, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "filter()", "description": "无法解析方法 'filter(&lt;method reference&gt;)'", "line": "174"}, {"issue_id": 91, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "map()", "description": "无法解析方法 'map(&lt;method reference&gt;)'", "line": "175"}, {"issue_id": 92, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model'", "line": "175"}, {"issue_id": 93, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "filter()", "description": "无法解析方法 'filter(&lt;method reference&gt;)'", "line": "175"}, {"issue_id": 94, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "flatMap()", "description": "无法解析方法 'flatMap(&lt;method reference&gt;)'", "line": "175"}, {"issue_id": 95, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "无法解析方法 'collect(Collector&lt;T, capture of ?, List&lt;T&gt;&gt;)'", "line": "176"}, {"issue_id": 96, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryTeamGroupEnergy()", "description": "不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'", "line": "180"}, {"issue_id": 97, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupEnergy,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "187"}, {"issue_id": 98, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "187"}, {"issue_id": 99, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Double&gt;'", "line": "187"}, {"issue_id": 100, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryUnitCoef()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'", "line": "189"}, {"issue_id": 101, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'", "line": "199"}, {"issue_id": 102, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getUnitEn()", "description": "无法解析方法 'getUnitEn()'", "line": "199"}, {"issue_id": 103, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "t()", "description": "'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'", "line": "203"}, {"issue_id": 104, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getLogTime()", "description": "无法解析方法 'getLogTime()'", "line": "203"}, {"issue_id": 105, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'", "line": "204"}, {"issue_id": 106, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "204"}, {"issue_id": 107, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamGroupId()", "description": "无法解析方法 'getTeamGroupId'", "line": "204"}, {"issue_id": 108, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "t()", "description": "'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'", "line": "214"}, {"issue_id": 109, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "214"}, {"issue_id": 110, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, ?)'", "line": "223"}, {"issue_id": 111, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "223"}, {"issue_id": 112, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesId()", "description": "无法解析方法 'getClassesId()'", "line": "223"}, {"issue_id": 113, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupNumber(java.lang.Integer)' 无法应用于 '(?)'", "line": "224"}, {"issue_id": 114, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getSerialNumber()", "description": "无法解析方法 'getSerialNumber()'", "line": "224"}, {"issue_id": 115, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'", "line": "228"}, {"issue_id": 116, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName()'", "line": "228"}, {"issue_id": 117, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setColor(java.lang.String)' 无法应用于 '(?)'", "line": "229"}, {"issue_id": 118, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getColor()", "description": "无法解析方法 'getColor()'", "line": "229"}, {"issue_id": 119, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "teamGroupEnergy()", "description": "'com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl' 中的 'unitConversion(java.lang.Double, UserDefineUnitDTO)' 无法应用于 '(?, UserDefineUnitDTO)'", "line": "231"}, {"issue_id": 120, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue()'", "line": "231"}, {"issue_id": 121, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'", "line": "237"}, {"issue_id": 122, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName()'", "line": "237"}, {"issue_id": 123, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setColor(java.lang.String)' 无法应用于 '(?)'", "line": "238"}, {"issue_id": 124, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getColor()", "description": "无法解析方法 'getColor()'", "line": "238"}, {"issue_id": 125, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "241"}, {"issue_id": 126, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "241"}, {"issue_id": 127, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryAssociationNodeById()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'", "line": "278"}, {"issue_id": 128, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "279"}, {"issue_id": 129, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "279"}, {"issue_id": 130, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "285"}, {"issue_id": 131, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'", "line": "286"}, {"issue_id": 132, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "286"}, {"issue_id": 133, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.List' 中的 'addAll(java.util.Collection&lt;? extends ClassesConfig&gt;)' 无法应用于 '(?)'", "line": "287"}, {"issue_id": 134, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "287"}, {"issue_id": 135, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "293"}, {"issue_id": 136, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId'", "line": "293"}, {"issue_id": 137, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'", "line": "293"}, {"issue_id": 138, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryClassesConfigDayEnergy()", "description": "不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'", "line": "297"}, {"issue_id": 139, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "304"}, {"issue_id": 140, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "304"}, {"issue_id": 141, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryUnitCoef()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'", "line": "305"}, {"issue_id": 142, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'", "line": "309"}, {"issue_id": 143, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "309"}, {"issue_id": 144, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesId()", "description": "无法解析方法 'getClassesId'", "line": "309"}, {"issue_id": 145, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "c()", "description": "'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'", "line": "324"}, {"issue_id": 146, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "324"}, {"issue_id": 147, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO.ClassesName' 中的 'setConfigName(java.lang.String)' 无法应用于 '(?)'", "line": "325"}, {"issue_id": 148, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName()'", "line": "325"}, {"issue_id": 149, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "328"}, {"issue_id": 150, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "stream()", "description": "无法解析方法 'stream()'", "line": "328"}, {"issue_id": 151, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "filter()", "description": "无法解析方法 'filter(&lt;lambda expression&gt;)'", "line": "329"}, {"issue_id": 152, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "329"}, {"issue_id": 153, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "stream()", "description": "无法解析方法 'stream()'", "line": "329"}, {"issue_id": 154, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "anyMatch()", "description": "无法解析方法 'anyMatch(&lt;lambda expression&gt;)'", "line": "330"}, {"issue_id": 155, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "config()", "description": "'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'", "line": "330"}, {"issue_id": 156, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "330"}, {"issue_id": 157, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "map()", "description": "无法解析方法 'map(&lt;method reference&gt;)'", "line": "330"}, {"issue_id": 158, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName'", "line": "330"}, {"issue_id": 159, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "find<PERSON>irst()", "description": "无法解析方法 'findFirst()'", "line": "331"}, {"issue_id": 160, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "orElse()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.String'", "line": "331"}, {"issue_id": 161, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "orElse()", "description": "无法解析方法 'or<PERSON>lse(String)'", "line": "331"}, {"issue_id": 162, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupEnergy,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "338"}, {"issue_id": 163, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamGroupId()", "description": "无法解析方法 'getTeamGroupId'", "line": "338"}, {"issue_id": 164, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'", "line": "341"}, {"issue_id": 165, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "345"}, {"issue_id": 166, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "stream()", "description": "无法解析方法 'stream()'", "line": "345"}, {"issue_id": 167, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "filter()", "description": "无法解析方法 'filter(&lt;lambda expression&gt;)'", "line": "346"}, {"issue_id": 168, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'", "line": "346"}, {"issue_id": 169, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "346"}, {"issue_id": 170, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "map()", "description": "无法解析方法 'map(&lt;method reference&gt;)'", "line": "347"}, {"issue_id": 171, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName'", "line": "347"}, {"issue_id": 172, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.String'", "line": "347"}, {"issue_id": 173, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "无法解析方法 'collect(Collector&lt;CharSequence, capture of ?, String&gt;)'", "line": "347"}, {"issue_id": 174, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'", "line": "352"}, {"issue_id": 175, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "352"}, {"issue_id": 176, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "354"}, {"issue_id": 177, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "354"}, {"issue_id": 178, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "357"}, {"issue_id": 179, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "357"}, {"issue_id": 180, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(()", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'", "line": "362"}, {"issue_id": 181, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getUnitEn()", "description": "无法解析方法 'getUnitEn()'", "line": "362"}, {"issue_id": 182, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "264"}, {"issue_id": 183, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(Double, ?, int)'", "line": "264"}, {"issue_id": 184, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getCoef()", "description": "无法解析方法 'getCoef()'", "line": "264"}, {"issue_id": 185, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getProjectTree()", "description": "不兼容的类型。实际为 null'，需要 'java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt;'", "line": "469"}, {"issue_id": 186, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getProjectTree()", "description": "无法解析 'EemNodeService' 中的方法 'getProjectTree'", "line": "469"}, {"issue_id": 187, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryBySchedulingSchemeId()", "description": "不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode&gt;'，需要 'java.util.List&lt;SchedulingSchemeToNode&gt;'", "line": "471"}, {"issue_id": 188, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getObjectId()", "description": "无法解析方法 'getObjectId()'", "line": "472"}, {"issue_id": 189, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getObjectLabel()", "description": "无法解析方法 'getObjectLabel()'", "line": "472"}]}}