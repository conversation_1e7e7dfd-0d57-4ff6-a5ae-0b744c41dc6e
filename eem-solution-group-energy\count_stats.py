#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def main():
    # 读取原始问题报告
    with open('output/method_issues_report.json', 'r', encoding='utf-8') as f:
        method_report = json.load(f)

    # 统计原始问题的实际数量（按类分组的结构）
    actual_original_count = 0
    for class_name, issues in method_report["issues_by_class"].items():
        actual_original_count += len(issues)

    # 读取分类后的文件
    with open('output/miss_method.json', 'r', encoding='utf-8') as f:
        miss_method = json.load(f)

    with open('output/wrong_params.json', 'r', encoding='utf-8') as f:
        wrong_params = json.load(f)

    with open('output/unidentified.json', 'r', encoding='utf-8') as f:
        unidentified = json.load(f)

    print('=== 问题分类统计 ===')
    print(f'原始问题总数（报告中）: {method_report["total_count"]}')
    print(f'原始问题总数（实际统计）: {actual_original_count}')
    print(f'miss_method 问题数: {len(miss_method)}')
    print(f'wrong_params 问题数: {len(wrong_params)}')
    print(f'unidentified 问题数: {len(unidentified)}')

    total = len(miss_method) + len(wrong_params) + len(unidentified)
    print(f'分类后总数: {total}')
    print(f'分类完整性（与报告数）: {total == method_report["total_count"]}')
    print(f'分类完整性（与实际数）: {total == actual_original_count}')

    # 如果不完整，显示缺失数量
    if total != actual_original_count:
        print(f'缺失问题数量: {actual_original_count - total}')

if __name__ == '__main__':
    main()
