[{"issue_id": 2, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamConfigServiceImpl", "missing_method": "queryUserBatch()", "description": "无法解析 'NodeAuthCheckService' 中的方法 'queryUserBatch'", "line": "535", "ai_analysis": "这个错误明确表示在 NodeAuthCheckService 类中找不到 queryUserBatch 方法。这是一个典型的缺失方法错误，可能的原因包括：1. 方法名称发生了变更（重命名）；2. 方法被删除或移动到其他类；3. 依赖版本不匹配导致方法不存在；4. 导入的类不正确。需要检查 NodeAuthCheckService 的当前 API 文档，查找替代方法或正确的方法名称。", "classification_reason": "错误信息明确指出'无法解析方法'，这是典型的方法缺失问题，属于 miss_method 分类"}, {"issue_id": 3, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "73", "ai_analysis": "错误表明在当前上下文中找不到 getTeamgroupinfo_model() 方法。这个方法名称看起来像是获取团队组信息模型的方法。可能的原因：1. 方法名称已更改；2. 方法被移动到其他类；3. 需要通过不同的对象或服务调用；4. 方法签名发生变化。需要查找相关的团队组信息获取方法的正确调用方式。", "classification_reason": "明确的'无法解析方法'错误信息，表示方法不存在，属于 miss_method 分类"}, {"issue_id": 5, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "78", "ai_analysis": "与 issue_id 3 相同的错误，在不同行号出现。表明 getTeamgroupinfo_model() 方法在多个地方被调用但都无法解析。这进一步确认了该方法确实不存在或已被重命名/移动。需要统一查找替代方法。", "classification_reason": "重复的'无法解析方法'错误，确认方法缺失，属于 miss_method 分类"}, {"issue_id": 7, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId'", "line": "79", "ai_analysis": "在 TeamGroupInfo 对象上调用 getId() 方法失败。这是一个常见的 getter 方法，通常用于获取实体的主键 ID。可能的原因：1. TeamGroupInfo 类中的 ID 字段名称不是 'id'；2. getter 方法名称不同（如 getTeamGroupId）；3. 字段访问方式发生变化；4. 类结构已更新。需要检查 TeamGroupInfo 类的实际字段和方法定义。", "classification_reason": "无法解析 getId 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 11, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "89", "ai_analysis": "在 TeamGroupEnergy 对象上调用 getValue() 方法失败。这个方法通常用于获取能源数值。可能的原因：1. 方法名称已更改（如改为 getEnergyValue 或 getAmount）；2. 字段名称变化导致 getter 方法名变化；3. 数据结构重构；4. 需要通过其他方式获取数值。需要检查 TeamGroupEnergy 类的当前结构和获取数值的正确方法。", "classification_reason": "无法解析 getValue 方法，表示方法不存在，属于 miss_method 分类"}, {"issue_id": 14, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "96", "ai_analysis": "无法找到具有特定签名 calcDouble(double, double, int) 的方法。这看起来是一个数值计算方法，可能用于能源数据的计算和转换。可能的原因：1. 方法签名已更改（参数类型或数量不同）；2. 方法被移动到工具类或其他服务；3. 方法名称已更改；4. 需要使用不同的计算方法或工具类。需要查找当前可用的数值计算方法。", "classification_reason": "无法解析具有特定签名的 calcDouble 方法，属于方法缺失，分类为 miss_method"}, {"issue_id": 17, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamGroupId()", "description": "无法解析方法 'getTeamGroupId'", "line": "101", "ai_analysis": "在 TeamGroupEnergy 对象上调用 getTeamGroupId() 方法失败。这个方法用于获取团队组的 ID。可能的原因：1. 字段名称不是 teamGroupId；2. getter 方法名称不同；3. 字段类型或访问方式发生变化；4. 需要通过关联对象获取。需要检查 TeamGroupEnergy 类的字段定义和正确的 getter 方法名称。", "classification_reason": "无法解析 getTeamGroupId 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 19, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "110", "ai_analysis": "又一个 getId() 方法无法解析的错误，这次在第110行。与之前的 issue_id 7 类似，表明在多个地方都存在 getId() 方法调用问题。这进一步确认了实体类的 ID 字段访问方式可能已经发生变化。需要统一检查和修复所有相关的 ID 获取方法调用。", "classification_reason": "重复的 getId 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 21, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName()'", "line": "111", "ai_analysis": "在对象上调用 getName() 方法失败。这是一个常见的 getter 方法，用于获取名称字段。可能的原因：1. 字段名称不是 'name'；2. getter 方法名称不同（如 getTeamGroupName）；3. 字段访问方式发生变化；4. 需要通过其他方式获取名称。需要检查相关实体类的字段定义和正确的名称获取方法。", "classification_reason": "无法解析 getName 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 23, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "114", "ai_analysis": "又一个 getValue() 方法无法解析的错误，与 issue_id 11 类似。这表明在多个地方都存在 getValue() 方法调用问题，进一步确认了 TeamGroupEnergy 类中获取数值的方法可能已经发生变化。需要统一查找和修复所有相关的数值获取方法调用。", "classification_reason": "重复的 getValue 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 25, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "118", "ai_analysis": "又一个 calcDouble(double, double, int) 方法无法解析的错误，与 issue_id 14 相同。这表明在多个地方都需要使用这个计算方法，但该方法确实不存在。需要统一查找替代的计算方法或工具类。", "classification_reason": "重复的 calcDouble 方法缺失错误，确认特定签名的方法不存在，分类为 miss_method"}, {"issue_id": 27, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(double, double, int)'", "line": "120", "ai_analysis": "第三个 calcDouble(double, double, int) 方法无法解析的错误。这进一步确认了该方法的缺失，并且在代码中被频繁使用。需要优先解决这个计算方法的替代方案。", "classification_reason": "重复的 calcDouble 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 29, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getUnitEn()", "description": "无法解析方法 'getUnitEn()'", "line": "141", "ai_analysis": "在 UserDefineUnitDTO 对象上调用 getUnitEn() 方法失败。这个方法看起来是用于获取单位的英文名称。可能的原因：1. 方法名称已更改（如 getUnitEnglish 或 getEnglishUnit）；2. 字段名称变化；3. 方法被移动或删除；4. 需要通过其他方式获取英文单位名称。需要检查 UserDefineUnitDTO 类的当前结构。", "classification_reason": "无法解析 getUnitEn 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 32, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "380", "ai_analysis": "无法找到 getClassesscheme_model() 方法。这个方法名称看起来是用于获取班次方案模型的方法。可能的原因：1. 方法名称已更改；2. 方法被移动到其他类或服务；3. 数据获取方式发生变化；4. 需要通过不同的 API 获取班次方案信息。需要查找当前获取班次方案数据的正确方法。", "classification_reason": "无法解析 getClassesscheme_model 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 33, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "386", "ai_analysis": "与 issue_id 32 相同的错误，在不同行号出现。进一步确认了 getClassesscheme_model() 方法的缺失，并且在多个地方被调用。需要统一查找替代方法。", "classification_reason": "重复的 getClassesscheme_model 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 35, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "387", "ai_analysis": "无法找到 getClassesconfig_model() 方法。这个方法看起来是用于获取班次配置模型的方法。与 getClassesscheme_model() 类似，可能是班次相关数据获取方式发生了变化。需要查找当前获取班次配置数据的正确方法。", "classification_reason": "无法解析 getClassesconfig_model 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 37, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "388", "ai_analysis": "与 issue_id 35 相同的错误，在不同行号出现。进一步确认了 getClassesconfig_model() 方法的缺失。", "classification_reason": "重复的 getClassesconfig_model 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 43, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "406", "ai_analysis": "又一个 getValue() 方法无法解析的错误。这是该方法的第四次出现，进一步确认了 TeamGroupEnergy 类中 getValue() 方法的缺失。这个方法在能源数据处理中被频繁使用，需要优先解决。", "classification_reason": "重复的 getValue 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 47, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getValue()", "description": "无法解析方法 'getValue'", "line": "415", "ai_analysis": "第五个 getValue() 方法无法解析的错误。这进一步确认了该方法的缺失，并且在代码中被广泛使用。", "classification_reason": "重复的 getValue 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 49, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(Double, double, int)'", "line": "417", "ai_analysis": "又一个 calcDouble 方法的变体，这次签名是 calcDouble(Double, double, int)，与之前的 calcDouble(double, double, int) 略有不同。这表明可能存在多个重载版本的需求，但都不存在。需要查找支持不同参数类型的计算方法。", "classification_reason": "无法解析 calcDouble 方法的另一个重载版本，属于方法缺失，分类为 miss_method"}, {"issue_id": 51, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "无法解析方法 'calcDouble(Double, double, int)'", "line": "419", "ai_analysis": "与 issue_id 49 相同的 calcDouble(Double, double, int) 方法缺失错误。这进一步确认了该重载版本的缺失。", "classification_reason": "重复的 calcDouble 方法缺失错误，确认特定重载版本不存在，分类为 miss_method"}, {"issue_id": 53, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getUnitEn()", "description": "无法解析方法 'getUnitEn()'", "line": "427", "ai_analysis": "与 issue_id 29 相同的 getUnitEn() 方法缺失错误，在不同行号出现。这进一步确认了该方法的缺失。", "classification_reason": "重复的 getUnitEn 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 55, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "430", "ai_analysis": "又一个 getTeamgroupinfo_model() 方法缺失错误。这是该方法的多次重复出现，进一步确认了该方法在系统中被广泛使用但确实不存在。需要统一查找替代的团队组信息获取方法。", "classification_reason": "重复的 getTeamgroupinfo_model 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 57, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId()'", "line": "433", "ai_analysis": "又一个 getId() 方法缺失错误。这是该方法的多次重复出现，表明在多个实体类中都存在 ID 获取方法的问题。需要统一检查所有相关实体类的 ID 字段访问方式。", "classification_reason": "重复的 getId 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 59, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName'", "line": "434", "ai_analysis": "又一个 getName() 方法缺失错误。这与之前的 issue_id 21 类似，表明在多个实体类中都存在名称获取方法的问题。需要统一检查所有相关实体类的名称字段访问方式。", "classification_reason": "重复的 getName 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 62, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "440", "ai_analysis": "又一个 getClassesscheme_model() 方法缺失错误。这与之前的 issue_id 32、33 相同，进一步确认了该方法的缺失并且被频繁使用。", "classification_reason": "重复的 getClassesscheme_model 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 64, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "441", "ai_analysis": "又一个 getClassesconfig_model() 方法缺失错误。这与之前的 issue_id 35、37 相同，进一步确认了该方法的缺失。", "classification_reason": "重复的 getClassesconfig_model 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 65, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesconfig_model()", "description": "无法解析方法 'getClassesconfig_model()'", "line": "444", "ai_analysis": "第四个 getClassesconfig_model() 方法缺失错误。这进一步确认了该方法在代码中被广泛使用但确实不存在。", "classification_reason": "重复的 getClassesconfig_model 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 66, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "stream()", "description": "无法解析方法 'stream()'", "line": "444", "ai_analysis": "无法解析 stream() 方法。这通常发生在集合对象上调用 stream() 方法失败。可能的原因：1. 调用对象不是集合类型；2. 调用对象为 null；3. 导入问题导致方法无法识别。需要检查调用 stream() 的对象类型和状态。", "classification_reason": "无法解析 stream 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 67, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "filter()", "description": "无法解析方法 'filter(<lambda expression>)'", "line": "445", "ai_analysis": "无法解析 Stream.filter() 方法。这通常是由于上游的 stream() 方法调用失败（issue_id 66）导致的连锁反应。filter() 方法本身是 Stream API 的标准方法，问题可能在于流对象的创建失败。", "classification_reason": "无法解析 filter 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 71, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "无法解析方法 'collect(Collector<T, capture of ?, List<T>>)'", "line": "445", "ai_analysis": "无法解析 Stream.collect() 方法的特定重载版本。这也是由于上游流操作失败导致的连锁反应。collect() 方法本身是 Stream API 的标准方法，问题在于流对象的类型推断失败。", "classification_reason": "无法解析 collect 方法的特定重载，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 73, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName()'", "line": "450", "ai_analysis": "又一个 getName() 方法缺失错误。这是该方法的多次重复出现，进一步确认了在多个实体类中都存在名称获取方法的问题。", "classification_reason": "重复的 getName 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 75, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getName()", "description": "无法解析方法 'getName'", "line": "451", "ai_analysis": "又一个 getName() 方法缺失错误，与 issue_id 73 紧邻出现，表明在同一段代码中多次调用了不存在的 getName() 方法。", "classification_reason": "重复的 getName 方法缺失错误，确认方法不存在，分类为 miss_method"}, {"issue_id": 144, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesId()", "description": "无法解析方法 'getClassesId'", "line": "309", "ai_analysis": "无法解析 getClassesId() 方法。这个方法用于获取班次配置的 ID。可能的原因：1. 字段名称不是 classesId；2. getter 方法名称不同；3. 字段访问方式发生变化；4. 需要通过其他方式获取班次 ID。需要检查相关实体类的字段定义。", "classification_reason": "无法解析 getClassesId 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 154, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "anyMatch()", "description": "无法解析方法 'anyMatch(<lambda expression>)'", "line": "330", "ai_analysis": "无法解析 Stream.anyMatch() 方法。这是 Stream API 的标准方法，问题可能在于上游的流操作失败导致的连锁反应。anyMatch() 用于检查流中是否有任何元素匹配给定条件。", "classification_reason": "无法解析 anyMatch 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 159, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "find<PERSON>irst()", "description": "无法解析方法 'findFirst()'", "line": "331", "ai_analysis": "无法解析 Stream.findFirst() 方法。这是 Stream API 的标准方法，用于获取流中的第一个元素。问题可能在于上游的流操作失败导致的连锁反应。", "classification_reason": "无法解析 findFirst 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 161, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "orElse()", "description": "无法解析方法 'or<PERSON>lse(String)'", "line": "331", "ai_analysis": "无法解析 Optional.orElse() 方法。这是 Optional API 的标准方法，用于在值不存在时提供默认值。问题可能在于上游的 findFirst() 方法调用失败（issue_id 159）导致的连锁反应。", "classification_reason": "无法解析 orElse 方法，属于方法缺失问题，分类为 miss_method"}, {"issue_id": 80, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "164", "ai_analysis": "重复的 getTeamgroupinfo_model() 方法缺失错误，进一步确认该方法在系统中被广泛使用但不存在。", "classification_reason": "重复的 getTeamgroupinfo_model 方法缺失错误，分类为 miss_method"}, {"issue_id": 82, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "无法解析方法 'getTeamgroupinfo_model()'", "line": "169", "ai_analysis": "又一个 getTeamgroupinfo_model() 方法缺失错误，该方法在代码中被频繁调用。", "classification_reason": "重复的 getTeamgroupinfo_model 方法缺失错误，分类为 miss_method"}, {"issue_id": 84, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getId()", "description": "无法解析方法 'getId'", "line": "170", "ai_analysis": "又一个 getId() 方法缺失错误，该方法在实体类中被广泛使用但不存在。", "classification_reason": "重复的 getId 方法缺失错误，分类为 miss_method"}, {"issue_id": 87, "error_code": "miss_method", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getClassesscheme_model()", "description": "无法解析方法 'getClassesscheme_model()'", "line": "173", "ai_analysis": "又一个 getClassesscheme_model() 方法缺失错误，该方法在班次相关业务中被频繁使用。", "classification_reason": "重复的 getClassesscheme_model 方法缺失错误，分类为 miss_method"}]