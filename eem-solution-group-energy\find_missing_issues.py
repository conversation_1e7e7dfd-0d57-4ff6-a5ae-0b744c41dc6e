#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def main():
    # 读取原始问题报告
    with open('output/method_issues_report.json', 'r', encoding='utf-8') as f:
        method_report = json.load(f)
        
    # 读取分类后的文件
    with open('output/miss_method.json', 'r', encoding='utf-8') as f:
        miss_method = json.load(f)
        
    with open('output/wrong_params.json', 'r', encoding='utf-8') as f:
        wrong_params = json.load(f)
        
    with open('output/unidentified.json', 'r', encoding='utf-8') as f:
        unidentified = json.load(f)

    # 收集所有已分类的问题ID
    classified_ids = set()
    
    for issue in miss_method:
        classified_ids.add((issue['class'], issue['issue_id']))
        
    for issue in wrong_params:
        classified_ids.add((issue['class'], issue['issue_id']))
        
    for issue in unidentified:
        classified_ids.add((issue['class'], issue['issue_id']))

    # 找出所有原始问题
    all_original_issues = []
    for class_name, issues in method_report["issues_by_class"].items():
        for issue in issues:
            all_original_issues.append((class_name, issue['issue_id'], issue))

    # 找出遗漏的问题
    missing_issues = []
    for class_name, issue_id, issue in all_original_issues:
        if (class_name, issue_id) not in classified_ids:
            missing_issues.append((class_name, issue_id, issue))

    print(f'=== 遗漏问题分析 ===')
    print(f'总原始问题数: {len(all_original_issues)}')
    print(f'已分类问题数: {len(classified_ids)}')
    print(f'遗漏问题数: {len(missing_issues)}')
    
    if missing_issues:
        print('\n=== 遗漏问题详情（前10个）===')
        for i, (class_name, issue_id, issue) in enumerate(missing_issues[:10]):
            print(f'{i+1}. 类: {class_name}, ID: {issue_id}')
            print(f'   描述: {issue["description"]}')
            print(f'   行号: {issue["line"]}')
            print()

if __name__ == '__main__':
    main()
