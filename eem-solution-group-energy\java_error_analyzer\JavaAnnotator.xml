<problems is_local_tool="true">
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/config/EemFusionGroupEnergyBeanNameGenerator.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.config</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.config.EemFusionGroupEnergyBeanNameGenerator java.lang.String generateBeanName(org.springframework.beans.factory.config.BeanDefinition definition, org.springframework.beans.factory.support.BeanDefinitionRegistry registry)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'GroupEnergy'</description>
  <highlighted_element>GroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>29</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/config/EemFusionGroupEnergyBeanNameGenerator.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.config</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.config.EemFusionGroupEnergyBeanNameGenerator java.lang.String generateBeanName(org.springframework.beans.factory.config.BeanDefinition definition, org.springframework.beans.factory.support.BeanDefinitionRegistry registry)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'PLUGIN_NAME_PREFIX'</description>
  <highlighted_element>PLUGIN_NAME_PREFIX</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/config/PluginConfiguration.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.config</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.config.PluginConfiguration com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo getPluginRuntimeInfo()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'GroupEnergy'</description>
  <highlighted_element>GroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>59</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/config/PluginConfiguration.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.config</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.config.PluginConfiguration com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo getPluginRuntimeInfo()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'PLUGIN_NAME_PREFIX'</description>
  <highlighted_element>PLUGIN_NAME_PREFIX</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>18</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/config/PluginConfiguration.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.config</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.config.PluginConfiguration com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo getPluginRuntimeInfo()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.electric.fusion.matrix.v2.dto.business.PluginInfo' 中的 'setPluginname(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/config/PluginConfiguration.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.config</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.config.PluginConfiguration com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo getPluginRuntimeInfo()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'GroupEnergy'</description>
  <highlighted_element>GroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/config/PluginConfiguration.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.config</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.config.PluginConfiguration com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo getPluginRuntimeInfo()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'PLUGIN_NAME_PREFIX'</description>
  <highlighted_element>PLUGIN_NAME_PREFIX</highlighted_element>
  <language>JAVA</language>
  <offset>66</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>35</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>35</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>77</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>35</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'CLASSES_SCHEME'</description>
  <highlighted_element>CLASSES_SCHEME</highlighted_element>
  <language>JAVA</language>
  <offset>90</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'CLASSES_CONFIG'</description>
  <highlighted_element>CLASSES_CONFIG</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingSchemeToNode.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode SchedulingSchemeToNode()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingSchemeToNode.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode SchedulingSchemeToNode()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SCHEDULING_SCHEME_TO_NODE'</description>
  <highlighted_element>SCHEDULING_SCHEME_TO_NODE</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>47</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl com.cet.electric.commons.ApiResult&lt;java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt;&gt; pageQuery(com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>47</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl com.cet.electric.commons.ApiResult&lt;java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt;&gt; pageQuery(com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>77</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>47</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl com.cet.electric.commons.ApiResult&lt;java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt;&gt; pageQuery(com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SCHEDULING_SCHEME'</description>
  <highlighted_element>SCHEDULING_SCHEME</highlighted_element>
  <language>JAVA</language>
  <offset>90</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>77</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SCHEDULING_SCHEME'</description>
  <highlighted_element>SCHEDULING_SCHEME</highlighted_element>
  <language>JAVA</language>
  <offset>90</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>36</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl childNodeLabel" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>69</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>36</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl childNodeLabel" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'CLASSES_SCHEME'</description>
  <highlighted_element>CLASSES_SCHEME</highlighted_element>
  <language>JAVA</language>
  <offset>82</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>36</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl childNodeLabel" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>98</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>36</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl childNodeLabel" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'CLASSES_CONFIG'</description>
  <highlighted_element>CLASSES_CONFIG</highlighted_element>
  <language>JAVA</language>
  <offset>111</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl childNodeLabel" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl childNodeLabel" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TEAM_GROUP_INFO'</description>
  <highlighted_element>TEAM_GROUP_INFO</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>72</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt; queryAll()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>72</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt; queryAll()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>77</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>72</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt; queryAll()" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SCHEDULING_SCHEME'</description>
  <highlighted_element>SCHEDULING_SCHEME</highlighted_element>
  <language>JAVA</language>
  <offset>90</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>103</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>103</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TableNameDef'</description>
  <highlighted_element>TableNameDef</highlighted_element>
  <language>JAVA</language>
  <offset>77</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>103</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SCHEDULING_SCHEME'</description>
  <highlighted_element>SCHEDULING_SCHEME</highlighted_element>
  <language>JAVA</language>
  <offset>90</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>278</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>278</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>278</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'</description>
  <highlighted_element>queryAssociationNodeById</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>279</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>279</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>284</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>284</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>285</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>285</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>285</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>60</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>286</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>42</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>286</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model()'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>287</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.List' 中的 'addAll(java.util.Collection&lt;? extends ClassesConfig&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>287</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model()'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>52</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>293</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>65</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>293</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>66</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>293</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>293</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>88</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>297</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>297</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>297</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'</description>
  <highlighted_element>queryClassesConfigDayEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>304</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>67</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>304</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>68</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>304</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>85</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>305</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>305</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>305</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'</description>
  <highlighted_element>queryUnitCoef</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>308</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>308</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>309</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>309</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>309</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>309</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesId'</description>
  <highlighted_element>getClassesId</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>323</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>323</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>324</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'</description>
  <highlighted_element>c</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>324</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>325</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO.ClassesName' 中的 'setConfigName(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>86</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>325</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName()'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>101</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>328</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>49</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>328</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'stream()'</description>
  <highlighted_element>stream</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>329</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'filter(&lt;lambda expression&gt;)'</description>
  <highlighted_element>filter</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>329</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model()'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>45</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>329</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'stream()'</description>
  <highlighted_element>stream</highlighted_element>
  <language>JAVA</language>
  <offset>70</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'anyMatch(&lt;lambda expression&gt;)'</description>
  <highlighted_element>anyMatch</highlighted_element>
  <language>JAVA</language>
  <offset>29</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'</description>
  <highlighted_element>config</highlighted_element>
  <language>JAVA</language>
  <offset>63</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>70</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'map(&lt;method reference&gt;)'</description>
  <highlighted_element>map</highlighted_element>
  <language>JAVA</language>
  <offset>92</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>96</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>111</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>331</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'findFirst()'</description>
  <highlighted_element>findFirst</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>9</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>331</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.String'</description>
  <highlighted_element>orElse</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>331</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'orElse(String)'</description>
  <highlighted_element>orElse</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>338</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupEnergy,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>338</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>338</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamGroupId'</description>
  <highlighted_element>getTeamGroupId</highlighted_element>
  <language>JAVA</language>
  <offset>42</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>341</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>345</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamgroupinfo_model()'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>345</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'stream()'</description>
  <highlighted_element>stream</highlighted_element>
  <language>JAVA</language>
  <offset>82</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>346</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'filter(&lt;lambda expression&gt;)'</description>
  <highlighted_element>filter</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>346</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>346</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>61</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>347</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'map(&lt;method reference&gt;)'</description>
  <highlighted_element>map</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>347</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>29</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>347</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>347</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.String'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>53</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>347</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'collect(Collector&lt;CharSequence, capture of ?, String&gt;)'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>53</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>352</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>78</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>352</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>79</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>352</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>96</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>354</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>354</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>354</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(double, double, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>357</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>357</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>357</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(double, double, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>362</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>362</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getUnitEn()'</description>
  <highlighted_element>getUnitEn</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>9</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>72</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>72</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>72</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'</description>
  <highlighted_element>queryAssociationNodeById</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>73</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>73</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamgroupinfo_model()'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>78</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>78</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>78</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>78</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamgroupinfo_model()'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>79</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>79</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>79</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>73</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>79</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>80</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>82</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>82</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>82</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'</description>
  <highlighted_element>queryTeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>20</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>89</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>69</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>89</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>70</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>89</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>87</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>90</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>90</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>90</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'</description>
  <highlighted_element>queryUnitCoef</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>96</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>96</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>96</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(double, double, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>100</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>100</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>101</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>101</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>101</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>101</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamGroupId'</description>
  <highlighted_element>getTeamGroupId</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>104</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>104</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>108</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>108</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>110</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>110</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>110</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'</description>
  <highlighted_element>t</highlighted_element>
  <language>JAVA</language>
  <offset>91</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>110</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>111</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>66</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>111</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName()'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>114</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>114</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>72</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>114</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>118</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>118</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>118</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(double, double, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>120</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>120</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>120</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(double, double, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>141</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>141</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getUnitEn()'</description>
  <highlighted_element>getUnitEn</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>9</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>49</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>28</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>379</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>379</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>379</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'</description>
  <highlighted_element>queryAssociationNodeById</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>380</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>380</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>385</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>385</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>386</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>386</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>386</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>60</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>387</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>42</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>387</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model()'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>388</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.List' 中的 'addAll(java.util.Collection&lt;? extends ClassesConfig&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>388</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model()'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>52</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>395</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>395</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>395</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>36</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>396</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>399</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>399</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>399</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'</description>
  <highlighted_element>queryClassesConfigDayEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>62</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>406</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>406</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>59</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>406</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>407</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>407</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>407</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'</description>
  <highlighted_element>queryUnitCoef</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>410</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>410</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>410</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'</description>
  <highlighted_element>queryClassesConfigDayEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>415</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>61</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>415</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>62</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>415</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>79</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>417</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>417</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>417</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(Double, double, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>419</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>419</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>419</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(Double, double, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>427</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>427</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getUnitEn()'</description>
  <highlighted_element>getUnitEn</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>9</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>430</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>430</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>430</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>65</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>430</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamgroupinfo_model()'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>65</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>433</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>108</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>433</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>111</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>434</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>434</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>434</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>40</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>434</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 R'，需要 'java.lang.String'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>49</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>434</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'collect(java.util.stream.Collector&lt;? super java.lang.Object,A,R&gt;)' 无法应用于 '(java.util.stream.Collector&lt;java.lang.CharSequence,capture&lt;?&gt;,java.lang.String&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>56</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>440</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>440</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>440</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>60</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>441</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>441</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model()'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>444</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>444</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>444</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model()'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>56</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>444</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'stream()'</description>
  <highlighted_element>stream</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>445</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'filter(&lt;lambda expression&gt;)'</description>
  <highlighted_element>filter</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>445</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>70</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>445</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>73</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>445</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.util.List&lt;ClassesConfig&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>83</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>445</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'collect(Collector&lt;T, capture of ?, List&lt;T&gt;&gt;)'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>83</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>450</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO.ClassesName' 中的 'setSchemeName(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>450</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName()'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>52</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>451</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>53</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>451</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>451</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>69</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>451</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 R'，需要 'java.lang.String'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>78</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>451</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO queryClassesEnergy(com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'collect(java.util.stream.Collector&lt;? super java.lang.Object,A,R&gt;)' 无法应用于 '(java.util.stream.Collector&lt;java.lang.CharSequence,capture&lt;?&gt;,java.lang.String&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>85</offset>
  <length>1</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>469</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt;'</description>
  <highlighted_element>getProjectTree</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>469</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析 'EemNodeService' 中的方法 'getProjectTree'</description>
  <highlighted_element>getProjectTree</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>471</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SchedulingSchemeToNode'</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>471</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'SchedulingSchemeToNode'</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>471</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode&gt;'，需要 'java.util.List&lt;SchedulingSchemeToNode&gt;'</description>
  <highlighted_element>queryBySchedulingSchemeId</highlighted_element>
  <language>JAVA</language>
  <offset>69</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>472</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析构造函数 'BaseVo(?, ?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>73</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>472</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getObjectId()'</description>
  <highlighted_element>getObjectId</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>472</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getObjectLabel()'</description>
  <highlighted_element>getObjectLabel</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>14</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>163</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>163</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'SchedulingScheme'</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>163</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'</description>
  <highlighted_element>queryAssociationNodeById</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>164</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>164</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamgroupinfo_model()'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>169</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>169</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>169</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>169</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamgroupinfo_model()'</description>
  <highlighted_element>getTeamgroupinfo_model</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>170</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>170</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>170</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>73</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>170</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>80</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>172</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>172</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>173</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>173</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>56</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>174</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesscheme_model()'</description>
  <highlighted_element>getClassesscheme_model</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>174</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'stream()'</description>
  <highlighted_element>stream</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>174</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'filter(&lt;method reference&gt;)'</description>
  <highlighted_element>filter</highlighted_element>
  <language>JAVA</language>
  <offset>80</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>175</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'map(&lt;method reference&gt;)'</description>
  <highlighted_element>map</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>175</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesScheme'</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>175</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesconfig_model'</description>
  <highlighted_element>getClassesconfig_model</highlighted_element>
  <language>JAVA</language>
  <offset>40</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>175</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'filter(&lt;method reference&gt;)'</description>
  <highlighted_element>filter</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>175</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'flatMap(&lt;method reference&gt;)'</description>
  <highlighted_element>flatMap</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>176</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'collect(Collector&lt;T, capture of ?, List&lt;T&gt;&gt;)'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>180</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>180</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>180</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'</description>
  <highlighted_element>queryTeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>20</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>187</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupEnergy,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>187</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>72</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>187</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>187</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Double&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>99</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>189</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>189</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>189</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'</description>
  <highlighted_element>queryUnitCoef</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>199</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>199</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getUnitEn()'</description>
  <highlighted_element>getUnitEn</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>9</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>202</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>202</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>203</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'</description>
  <highlighted_element>t</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>203</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getLogTime()'</description>
  <highlighted_element>getLogTime</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'</description>
  <highlighted_element>collect</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getTeamGroupId'</description>
  <highlighted_element>getTeamGroupId</highlighted_element>
  <language>JAVA</language>
  <offset>68</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>208</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>208</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>211</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>211</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>214</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>214</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupInfo'</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>214</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'</description>
  <highlighted_element>t</highlighted_element>
  <language>JAVA</language>
  <offset>95</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>214</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>97</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>219</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>219</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>222</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>222</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'ClassesConfig'</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>223</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, ?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>59</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>223</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getId()'</description>
  <highlighted_element>getId</highlighted_element>
  <language>JAVA</language>
  <offset>62</offset>
  <length>5</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>223</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getClassesId()'</description>
  <highlighted_element>getClassesId</highlighted_element>
  <language>JAVA</language>
  <offset>87</offset>
  <length>12</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>224</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupNumber(java.lang.Integer)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>96</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>224</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getSerialNumber()'</description>
  <highlighted_element>getSerialNumber</highlighted_element>
  <language>JAVA</language>
  <offset>111</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>228</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>228</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName()'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>79</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>229</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setColor(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>56</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>229</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getColor()'</description>
  <highlighted_element>getColor</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>231</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl' 中的 'unitConversion(java.lang.Double, UserDefineUnitDTO)' 无法应用于 '(?, UserDefineUnitDTO)'</description>
  <highlighted_element>teamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>87</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>231</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue()'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>103</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>237</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>60</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>237</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getName()'</description>
  <highlighted_element>getName</highlighted_element>
  <language>JAVA</language>
  <offset>75</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>238</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setColor(java.lang.String)' 无法应用于 '(?)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>52</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>238</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getColor()'</description>
  <highlighted_element>getColor</highlighted_element>
  <language>JAVA</language>
  <offset>67</offset>
  <length>8</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>241</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'</description>
  <highlighted_element>(</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>1</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>241</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'TeamGroupEnergy'</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>72</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>241</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getValue'</description>
  <highlighted_element>getValue</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>8</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>260</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.lang.Double unitConversion(java.lang.Double value, UserDefineUnitDTO unit)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>260</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.lang.Double unitConversion(java.lang.Double value, UserDefineUnitDTO unit)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>未知类: 'UserDefineUnitDTO'</description>
  <highlighted_element>UserDefineUnitDTO</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>264</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.lang.Double unitConversion(java.lang.Double value, UserDefineUnitDTO unit)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析符号 'NumberCalcUtils'</description>
  <highlighted_element>NumberCalcUtils</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>264</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.lang.Double unitConversion(java.lang.Double value, UserDefineUnitDTO unit)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'java.lang.Double'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>264</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.lang.Double unitConversion(java.lang.Double value, UserDefineUnitDTO unit)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'calcDouble(Double, ?, int)'</description>
  <highlighted_element>calcDouble</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>264</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.lang.Double unitConversion(java.lang.Double value, UserDefineUnitDTO unit)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析方法 'getCoef()'</description>
  <highlighted_element>getCoef</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>7</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>535</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>不兼容的类型。实际为 null'，需要 'com.cet.eem.fusion.common.entity.Result&lt;java.util.List&lt;com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo&gt;&gt;'</description>
  <highlighted_element>queryUserBatch</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>535</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="JavaAnnotator" severity="ERROR" attribute_key="ERRORS_ATTRIBUTES">Java 注解器</problem_class>
  <description>无法解析 'NodeAuthCheckService' 中的方法 'queryUserBatch'</description>
  <highlighted_element>queryUserBatch</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>14</length>
</problem>
</problems>