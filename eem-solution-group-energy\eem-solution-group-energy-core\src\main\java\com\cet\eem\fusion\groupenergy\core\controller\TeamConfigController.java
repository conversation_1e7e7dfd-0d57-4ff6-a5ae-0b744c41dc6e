package com.cet.eem.fusion.groupenergy.core.controller;

import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.config.sdk.service.log.OperationLog;
import com.cet.eem.fusion.common.utils.EnumOperationSubType;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.groupenergy.core.def.GroupEnergyConstantDef;
import com.cet.eem.fusion.groupenergy.core.entity.dto.*;
import com.cet.eem.fusion.groupenergy.core.entity.vo.*;
import com.cet.eem.fusion.groupenergy.core.service.TeamConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * 班组配置相关接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@Api(value = "/groupenergy/v1/config", tags = "班组配置相关接口")
@RequestMapping(value = "/groupenergy/v1/config")
public class TeamConfigController {

    @Autowired
    private TeamConfigService teamConfigService;

    @PostMapping("/schedulingscheme/addOrUpdate")
    @ApiOperation("新增修改排班方案")
    @OperationLog(operationType = GroupEnergyConstantDef.SCHEDULING_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【新增编辑排班方案】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_INSPECT, GroupEnergyConstantDef.SCHEDULINGSCHEME_UPDATE}, andOr = EnumAndOr.OR)
    public ApiResult<Boolean> addOrUpdateSchedulingScheme(@RequestBody SchedulingSchemeAddUpdateDTO dto) {
        return Result.ok(teamConfigService.addOrUpdateSchedulingScheme(dto));
    }

    @PostMapping("/schedulingscheme/query")
    @ApiOperation("根据条件查询排班方案")
    public ApiResult<List<SchedulingSchemeDetailVO>> querySchedulingScheme(@RequestBody SchedulingSchemeQueryDTO dto) {
        return teamConfigService.querySchedulingScheme(dto);
    }

    @GetMapping("/schedulingscheme/type/query")
    @ApiOperation("根据方案类型查询排班方案")
    public ApiResult<List<SchedulingSchemeDetailVO>> querySchedulingSchemeByType(@RequestParam(required = false) @ApiParam(name = "classTeamType", value = "排班方案类型", required = false)
                                                                                      Integer classTeamType) {
        return Result.ok(teamConfigService.queryProduceSchedulingSchemeByType(classTeamType));
    }

    @GetMapping("/schedulingscheme/all/query")
    @ApiOperation("查询所有排班方案")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_BROWSER})
    public ApiResult<List<SchedulingSchemeDetailVO>> allSchedulingScheme() {
        return Result.ok(teamConfigService.allSchedulingScheme());
    }

    @DeleteMapping("/schedulingscheme/delete")
    @ApiOperation("删除排班方案")
    @OperationLog(operationType = GroupEnergyConstantDef.SCHEDULING_SCHEME, subType = EnumOperationSubType.DELETE, description = "【删除排班方案】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_DELETE})
    public void deleteSchedulingScheme(@RequestParam @ApiParam(name = "id", value = "排班方案id", required = true) Long id) {
        teamConfigService.deleteSchedulingScheme(id);
    }

    @PostMapping("/schedulingscheme/holiday/save")
    @ApiOperation("保存排班方案关联节假日")
    @OperationLog(operationType = GroupEnergyConstantDef.SCHEDULING_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【保存排班方案关联节假日】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_UPDATE})
    public ApiResult<Boolean> saveSchedulingSchemeRelatedHoliday(@RequestBody SchedulingSchemeRelatedHolidayDTO dto) {
        return Result.ok(teamConfigService.saveSchedulingSchemeRelatedHoliday(dto));
    }

    @GetMapping("/schedulingscheme/holiday/query")
    @ApiOperation("查询排班方案关联节假日")
    public ApiResult<List<Long>> querySchedulingSchemeRelatedHoliday(@RequestParam @ApiParam(name = "schedulingSchemeId", value = "排班方案id", required = true)
                                                                          Long schedulingSchemeId) {
        return Result.ok(teamConfigService.querySchedulingSchemeRelatedHoliday(schedulingSchemeId));
    }

    @PostMapping("/schedulingscheme/node/save")
    @ApiOperation("保存排班方案关联节点")
    @OperationLog(operationType = GroupEnergyConstantDef.SCHEDULING_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【保存排班方案关联节点】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_UPDATE})
    public ApiResult<Boolean> saveSchedulingSchemeRelatedNode(@RequestBody SchedulingSchemeRelatedNodeDTO dto) {
        return Result.ok(teamConfigService.saveSchedulingSchemeRelatedNode(dto));
    }

    @GetMapping("/schedulingscheme/node/query")
    @ApiOperation("查询排班方案关联节点")
    public ApiResult<List<BaseVo>> querySchedulingSchemeRelatedNode(@RequestParam @ApiParam(name = "schedulingSchemeId", value = "排班方案id", required = true)
                                                                         Long schedulingSchemeId) {
        return Result.ok(teamConfigService.querySchedulingSchemeRelatedNode(schedulingSchemeId));
    }

    @PostMapping("/classesscheme/addOrUpdate")
    @ApiOperation("新增修改班次方案")
    @OperationLog(operationType = GroupEnergyConstantDef.CLASSES_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【新增编辑班次方案】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_UPDATE})
    public ApiResult<Boolean> addOrUpdateClassesScheme(@RequestBody ClassesSchemeAddUpdateDTO dto) {
        return Result.ok(teamConfigService.addOrUpdateClassesScheme(dto));
    }

    @GetMapping("/classesscheme/query")
    @ApiOperation("查询班次方案")
    public ApiResult<List<ClassesSchemeVO>> queryClassesScheme(@RequestParam @ApiParam(name = "id", value = "排班方案id", required = true)
                                                                    Long id) {
        return Result.ok(teamConfigService.queryClassesScheme(id));
    }

    @DeleteMapping("/classesscheme/delete")
    @ApiOperation("删除班次方案")
    @OperationLog(operationType = GroupEnergyConstantDef.CLASSES_SCHEME, subType = EnumOperationSubType.DELETE, description = "【删除班次方案】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_DELETE})
    public void deleteClassesScheme(@RequestParam @ApiParam(name = "id", value = "班次方案id", required = true) Long id) {
        teamConfigService.deleteClassesScheme(id);
    }

    @PostMapping("/teamgroupinfo/addOrUpdate")
    @ApiOperation("新增修改班组")
    @OperationLog(operationType = GroupEnergyConstantDef.TEAM_GROUP_INFO, subType = EnumOperationSubType.UPDATE, description = "【新增编辑班组】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_UPDATE})
    public ApiResult<Boolean> addOrUpdateTeamGroupInfo(@RequestBody TeamGroupInfoAddUpdateDTO dto) {
        return Result.ok(teamConfigService.addOrUpdateTeamGroupInfo(dto));
    }

    @DeleteMapping("/teamgroupinfo/delete")
    @ApiOperation("删除班组")
    @OperationLog(operationType = GroupEnergyConstantDef.TEAM_GROUP_INFO, subType = EnumOperationSubType.DELETE, description = "【删除班组】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_DELETE})
    public void deleteTeamGroupInfo(@RequestParam @ApiParam(name = "id", value = "班组id", required = true) Long id) {
        teamConfigService.deleteTeamGroupInfo(id);
    }

    @GetMapping("/teamgroupinfo/query")
    @ApiOperation("查询班组")
    public ApiResult<List<TeamGroupInfoVO>> queryTeamGroupInfo(@RequestParam @ApiParam(name = "schedulingSchemeId", value = "排班方案id", required = true)
                                                                    Long schedulingSchemeId) {
        return Result.ok(teamConfigService.queryTeamGroupInfo(schedulingSchemeId));
    }

    @PostMapping("/schedulingclasses/save")
    @ApiOperation("保存排班表")
    @OperationLog(operationType = GroupEnergyConstantDef.SCHEDULING_CLASSES, subType = EnumOperationSubType.UPDATE, description = "【保存排班表】")
    @OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_UPDATE})
    public ApiResult<Boolean> saveSchedulingClasses(@RequestBody SchedulingClassesSaveDTO dto) {
        return Result.ok(teamConfigService.saveSchedulingClasses(dto));
    }

    @GetMapping("/schedulingclasses/query")
    @ApiOperation("查询排班表")
    public ApiResult<List<SchedulingClassesVO>> querySchedulingClasses(@RequestParam @ApiParam(name = "starTime", value = "开始时间", required = true)
                                                                            Long starTime,
                                                                    @RequestParam @ApiParam(name = "endTime", value = "结束时间", required = true)
                                                                            Long endTime,
                                                                    @RequestParam @ApiParam(name = "schedulingSchemeId", value = "排班方案id", required = true)
                                                                            Long schedulingSchemeId) {
        return Result.ok(teamConfigService.querySchedulingClasses(starTime, endTime, schedulingSchemeId));
    }

    @GetMapping("/schedulingclasses/teamgroupinfo/query")
    @ApiOperation("查询时间范围内排班班组配置")
    public ApiResult<List<SchedulingClassesVO>> querySchedulingClassesTeamGroupInfo(@RequestParam @ApiParam(name = "starTime", value = "开始时间", required = true)
                                                                                         Long starTime,
                                                                                 @RequestParam @ApiParam(name = "endTime", value = "结束时间", required = true)
                                                                                         Long endTime,
                                                                                 @RequestParam @ApiParam(name = "schedulingSchemeId", value = "排班方案id", required = true)
                                                                                         Long schedulingSchemeId) {
        return Result.ok(teamConfigService.querySchedulingClassesTeamGroupInfo(starTime, endTime, schedulingSchemeId));
    }
}
