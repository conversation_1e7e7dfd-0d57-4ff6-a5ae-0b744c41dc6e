#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys

def analyze_report():
    """分析方法问题报告"""
    try:
        with open('method_issues_report.json', 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("=" * 60)
        print("方法问题报告分析")
        print("=" * 60)

        print(f"总问题数: {data['total_count']}")
        print(f"涉及类数: {len(data['issues_by_class'])}")

        # 按问题数量排序类
        sorted_classes = sorted(data['issues_by_class'].items(),
                               key=lambda x: len(x[1]), reverse=True)

        print("\n问题最多的前15个类:")
        print("-" * 40)
        for i, (cls, issues) in enumerate(sorted_classes[:15], 1):
            print(f"{i:2d}. {cls}: {len(issues)}个问题")

        # 统计错误类型
        error_types = {}
        for cls, issues in data['issues_by_class'].items():
            for issue in issues:
                error_code = issue.get('error_code', 'unknown')
                error_types[error_code] = error_types.get(error_code, 0) + 1

        print("\n错误类型统计:")
        print("-" * 40)
        for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
            print(f"{error_type}: {count}个")

        # 统计模块分布
        modules = {}
        for cls, issues in data['issues_by_class'].items():
            for issue in issues:
                module = issue.get('module', 'unknown')
                modules[module] = modules.get(module, 0) + 1

        print("\n模块分布:")
        print("-" * 40)
        for module, count in sorted(modules.items(), key=lambda x: x[1], reverse=True):
            print(f"{module}: {count}个问题")

        # 分析 TeamEnergyServiceImpl 的具体问题
        analyze_team_energy_service_impl(data)

        print("\n" + "=" * 60)

    except Exception as e:
        print(f"分析报告时出错: {e}")
        return False

    return True

def analyze_team_energy_service_impl(data):
    """分析 TeamEnergyServiceImpl 的具体问题"""
    team_energy_issues = data['issues_by_class'].get('TeamEnergyServiceImpl', [])
    if not team_energy_issues:
        return

    print(f"\nTeamEnergyServiceImpl 详细分析 ({len(team_energy_issues)}个问题):")
    print("-" * 50)

    # 统计缺失方法
    missing_methods = {}
    wrong_params = {}

    for issue in team_energy_issues:
        error_code = issue.get('error_code', 'unknown')
        method_name = issue.get('missing_method', 'unknown')
        description = issue.get('description', '')

        if error_code == 'miss_method':
            missing_methods[method_name] = missing_methods.get(method_name, 0) + 1
        elif error_code == 'wrong_params':
            wrong_params[description] = wrong_params.get(description, 0) + 1

    print("缺失方法统计 (前10个):")
    for method, count in sorted(missing_methods.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {method}: {count}次")

    print("\n参数错误统计 (前5个):")
    for desc, count in sorted(wrong_params.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {count}次: {desc[:80]}...")

    # 统计涉及的行数范围
    lines = [int(issue.get('line', 0)) for issue in team_energy_issues if issue.get('line', '').isdigit()]
    if lines:
        print(f"\n涉及代码行数范围: {min(lines)} - {max(lines)}")
        print(f"问题分布在 {max(lines) - min(lines) + 1} 行代码中")

if __name__ == "__main__":
    analyze_report()
