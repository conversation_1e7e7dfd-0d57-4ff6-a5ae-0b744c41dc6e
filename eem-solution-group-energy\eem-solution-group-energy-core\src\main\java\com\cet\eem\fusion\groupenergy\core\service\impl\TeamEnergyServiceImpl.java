package com.cet.eem.fusion.groupenergy.core.service.impl;

import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;
import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitDTO;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.def.common.EnumOperationType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.solution.common.utils.DoubleUtils;
import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao;
import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao;
import com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao;
// import com.cet.piem.entity.classes.*;
import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;
import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;
import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;
import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard;
import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;
import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;
import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;
import com.cet.eem.fusion.config.sdk.service.EemNodeService;
import com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService;
import com.cet.eem.fusion.common.utils.unit.NumberCalcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 班组能耗查询业务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TeamEnergyServiceImpl implements TeamEnergyService {

    @Autowired
    private SchedulingSchemeDao schedulingSchemeDao;

    @Autowired
    private TeamGroupEnergyDao teamGroupEnergyDao;

    @Autowired
    private EnergyUnitService energyUnitService;

    @Autowired
    private EemNodeService eemNodeService;

    @Autowired
    private SchedulingSchemeToNodeDao schemeToNodeDao;

    /**
     * 查询班组能耗数据
     *
     * @param dto 查询条件
     * @return 班组能耗数据
     */
    @Override
    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {
        //初始化返回数据
        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();

        //查询排班方案
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());
        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {
            return vo;
        }

        //取出排版方案之下班组
        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();
        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());

        //查询班组能耗
        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),
                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);
        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {
            return vo;
        }

        //总能耗
        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();
        UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, energyTotal));

        //总班次
        Integer size = teamGroupEnergyList.size();

        //平均班次能耗
        Double avgEnergy = NumberCalcUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());


        //分班组统计能耗数据
        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()
                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));

        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();
        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {
            TeamGroupEnergyCard card = new TeamGroupEnergyCard();

            Long teamGroupId = teamGroupEnergyEntity.getKey();
            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();

            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();
            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));

            //班组总用能
            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();
            //班组总班次
            Integer classesTotal = teamGroupEnergies.size();
            //班组平均班次用能
            Double teamAvgEnergy = NumberCalcUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());
            //用能占比
            Double energyProportion = NumberCalcUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());

            card.setTeamGroupId(teamGroupId);
            card.setClassesTotal(classesTotal);
            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));
            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));
            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));

            teamGroupEnergyCardList.add(card);
        }

        //按照平均班次能耗升序排序后，再保留两位小数
        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()
                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)
                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))
                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))
                .collect(Collectors.toList());

        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));
        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));
        vo.setClassesTotal(size);
        vo.setEnergyUnit(unit.getUnitEn());
        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);


        return vo;
    }

    /**
     * 班组用能柱状图查询
     *
     * @param dto 查询条件
     * @return 班组用能柱状图
     */
    @Override
    public List<TeamGroupEnergyHistogramVO> queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto) {
        Integer cycle;
        if (Objects.equals(AggregationCycle.ONE_MONTH, dto.getAggregationCycle())) {
            cycle = AggregationCycle.ONE_DAY;
        } else {
            cycle = AggregationCycle.ONE_MONTH;
        }
        //查询排班方案
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());
        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {
            return Collections.emptyList();
        }

        //取出排班方案之下班组
        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();
        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());

        List<ClassesConfig> classesConfigs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(schedulingScheme.getClassesscheme_model())) {
            classesConfigs = schedulingScheme.getClassesscheme_model().stream().filter(Objects::nonNull)
                    .map(ClassesScheme::getClassesconfig_model).filter(Objects::nonNull).flatMap(Collection::stream)
                    .collect(Collectors.toList());
        }

        //查询班组能耗
        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),
                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, cycle);
        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {
            return Collections.emptyList();
        }

        //查询单位
        List<Double> energyValueList = teamGroupEnergyList.stream().map(TeamGroupEnergy::getValue).collect(Collectors.toList());
        Double maxValue = energyValueList.stream().max(Double::compareTo).orElse(null);
        UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, maxValue));

        //拿到当月每天
        List<Long> timeRange = TimeUtil.getTimeRange(dto.getStartTime(), dto.getEndTime(), cycle);
        List<TeamGroupEnergyHistogramVO> vos = new ArrayList<>();
        for (Long logTime : timeRange) {

            TeamGroupEnergyHistogramVO vo = new TeamGroupEnergyHistogramVO();
            //设置时间
            vo.setLogTime(logTime);
            vo.setEnergyUnit(unit.getUnitEn());

            //取出当前时间班组数据，并根据班组分组
            Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()
                    .filter(t -> Objects.equals(t.getLogTime(), logTime))
                    .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));

            //设置当日的每个班组的能耗数据
            List<TeamGroupEnergyHistogramVO.TeamGroupEnergy> teamGroupEnergyInfos = new ArrayList<>();
            for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {

                Long teamGroupId = teamGroupEnergyEntity.getKey();
                List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();

                //取出班组的名称
                Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();

                //如果是查天的数据
                if (Objects.equals(AggregationCycle.ONE_DAY, cycle)) {
                    //每个班组都是单独一条数据
                    for (TeamGroupEnergy teamGroupEnergy : teamGroupEnergies) {
                        TeamGroupEnergyHistogramVO.TeamGroupEnergy teamGroupEnergyInfo = new TeamGroupEnergyHistogramVO.TeamGroupEnergy();

                        Optional<ClassesConfig> first1 = classesConfigs.stream()
                                .filter(c -> Objects.equals(c.getId(), teamGroupEnergy.getClassesId())).findFirst();
                        first1.ifPresent(classesConfig -> teamGroupEnergyInfo.setTeamGroupNumber(classesConfig.getSerialNumber()));

                        //班组的名称
                        first.ifPresent(teamGroupInfo -> {
                            teamGroupEnergyInfo.setTeamGroupName(teamGroupInfo.getName());
                            teamGroupEnergyInfo.setColor(teamGroupInfo.getColor());
                        });
                        teamGroupEnergyInfo.setEnergy(DoubleUtils.round(unitConversion(teamGroupEnergy.getValue(), unit), 2));
                        teamGroupEnergyInfos.add(teamGroupEnergyInfo);
                    }
                } else {
                    TeamGroupEnergyHistogramVO.TeamGroupEnergy teamGroupEnergyInfo = new TeamGroupEnergyHistogramVO.TeamGroupEnergy();
                    first.ifPresent(teamGroupInfo -> {
                        teamGroupEnergyInfo.setTeamGroupName(teamGroupInfo.getName());
                        teamGroupEnergyInfo.setColor(teamGroupInfo.getColor());
                    });
                    //计算班组的能耗
                    double sum = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();
                    //单位转换和保留小数
                    teamGroupEnergyInfo.setEnergy(DoubleUtils.round(unitConversion(sum, unit), 2));
                    teamGroupEnergyInfos.add(teamGroupEnergyInfo);
                }
            }

            teamGroupEnergyInfos.sort(Comparator.comparing(TeamGroupEnergyHistogramVO.TeamGroupEnergy::getTeamGroupNumber,
                    Comparator.nullsLast(Integer::compareTo)));

            vo.setTeamGroupEnergyList(teamGroupEnergyInfos);
            vos.add(vo);
        }

        return vos;
    }


    //单位转换
    private Double unitConversion(Double value, UserDefineUnitDTO unit) {
        if (Objects.isNull(value)) {
            return null;
        }
        return NumberCalcUtils.calcDouble(value, unit.getCoef(), EnumOperationType.DIVISION.getId());
    }

    /**
     * 查询班次对比能耗数据
     *
     * @param dto 查询条件
     * @return 班次能耗对比数据
     */
    @Override
    public List<ClassesEnergyInfoVO> queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto) {
        List<ClassesEnergyInfoVO> vos = new ArrayList<>();

        //查询排班方案
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());
        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getClassesscheme_model())) {
            return vos;
        }

        //取出所有班次
        List<ClassesConfig> classesConfigs = new ArrayList<>();
        for (ClassesScheme classesScheme : schedulingScheme.getClassesscheme_model()) {
            if (CollectionUtils.isNotEmpty(classesScheme.getClassesconfig_model())) {
                classesConfigs.addAll(classesScheme.getClassesconfig_model());
            }
        }
        if (CollectionUtils.isEmpty(classesConfigs)) {
            return vos;
        }
        List<Long> classesConfigIds = classesConfigs.stream().map(ClassesConfig::getId).collect(Collectors.toList());


        //查询班次能耗
        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryClassesConfigDayEnergy(dto.getStartTime(), dto.getEndTime(),
                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), classesConfigIds);
        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {
            return vos;
        }

        //班次总能耗，以及单位
        double energySum = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();
        UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, energySum));

        //根据班次能耗根据班次分组
        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyListGroup = teamGroupEnergyList.stream()
                .collect(Collectors.groupingBy(TeamGroupEnergy::getClassesId));

        //遍历班次能耗
        for (Long classesId : teamGroupEnergyListGroup.keySet()) {
            ClassesEnergyInfoVO vo = new ClassesEnergyInfoVO();

            //班次id
            vo.setClassesConfigIdList(Collections.singletonList(classesId));

            //当前班次能耗
            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyListGroup.get(classesId);

            //班次名称
            ClassesEnergyInfoVO.ClassesName classesName = new ClassesEnergyInfoVO.ClassesName();
            Optional<ClassesConfig> classesConfigOptional = classesConfigs.stream()
                    .filter(c -> Objects.equals(c.getId(), classesId)).findFirst();
            classesConfigOptional.ifPresent(classesConfig -> classesName.setConfigName(classesConfig.getName()));

            //班次方案名称
            String schemeName = schedulingScheme.getClassesscheme_model().stream()
                    .filter(scheme -> scheme.getClassesconfig_model().stream()
                            .anyMatch(config -> Objects.equals(config.getId(), classesId))).map(ClassesScheme::getName)
                    .findFirst().orElse("");
            classesName.setSchemeName(schemeName);
            vo.setClassesNames(Collections.singletonList(classesName));


            //班组id集合,
            List<Long> teamGroupIds = teamGroupEnergies.stream()
                    .map(TeamGroupEnergy::getTeamGroupId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(teamGroupIds)) {
                vo.setTeamGroupIdList(teamGroupIds);
                //班组名称拼接字符串
                String teamGroupNames = schedulingScheme.getTeamgroupinfo_model().stream()
                        .filter(t -> teamGroupIds.contains(t.getId()))
                        .map(TeamGroupInfo::getName).collect(Collectors.joining(","));
                vo.setTeamGroupNames(teamGroupNames);
            }

            //班组总用能量
            double teamGroupEnergySum = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();
            //用能占比
            Double energyProportion = NumberCalcUtils.calcDouble(teamGroupEnergySum, energySum, EnumOperationType.DIVISION.getId());
            //平均班次能耗
            Integer size = teamGroupEnergies.size();
            Double avgEnergy = NumberCalcUtils.calcDouble(teamGroupEnergySum, size.doubleValue(), EnumOperationType.DIVISION.getId());

            vo.setEnergyTotal(DoubleUtils.round(unitConversion(teamGroupEnergySum, unit), 2));
            vo.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));
            vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));
            vo.setEnergyUnit(unit.getUnitEn());
            vos.add(vo);
        }
        return vos;
    }

    /**
     * 根据条件查询班次能耗
     *
     * @param dto 查询条件
     * @return 班次能耗
     */
    @Override
    public ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto) {
        ClassesEnergyInfoVO vo = new ClassesEnergyInfoVO();

        //查询排班方案
        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());
        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getClassesscheme_model())) {
            return vo;
        }

        //取出所有班次
        List<ClassesConfig> classesConfigs = new ArrayList<>();
        for (ClassesScheme classesScheme : schedulingScheme.getClassesscheme_model()) {
            if (CollectionUtils.isNotEmpty(classesScheme.getClassesconfig_model())) {
                classesConfigs.addAll(classesScheme.getClassesconfig_model());
            }
        }
        if (CollectionUtils.isEmpty(classesConfigs)) {
            return vo;
        }
        List<Long> classesConfigIds = classesConfigs.stream()
                .map(ClassesConfig::getId)
                .collect(Collectors.toList());

        //查询班次能耗
        List<TeamGroupEnergy> energyList = teamGroupEnergyDao.queryClassesConfigDayEnergy(dto.getStartTime(), dto.getEndTime(),
                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), classesConfigIds);
        if (CollectionUtils.isEmpty(energyList)) {
            return vo;
        }

        //班次总能耗，以及单位
        double energySum = energyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();
        UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, energySum));

        //根据传递的班次和班组查询能耗
        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryClassesConfigDayEnergy(dto.getStartTime(), dto.getEndTime(),
                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), dto.getClassesConfigIdList(), dto.getTeamGroupIdList());
        //总班次数
        Integer size = teamGroupEnergyList.size();
        //总能耗
        Double sum = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();
        //平均班次能耗
        Double avgEnergy = NumberCalcUtils.calcDouble(sum, size.doubleValue(), EnumOperationType.DIVISION.getId());
        //用能占比
        Double energyProportion = NumberCalcUtils.calcDouble(sum, energySum, EnumOperationType.DIVISION.getId());
        vo.setEnergyTotal(DoubleUtils.round(unitConversion(sum, unit), 2));
        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));
        if (Objects.nonNull(energyProportion)) {
            vo.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));
        }
        vo.setClassesConfigIdList(dto.getClassesConfigIdList());
        vo.setTeamGroupIdList(dto.getTeamGroupIdList());
        vo.setEnergyUnit(unit.getUnitEn());

        //班组名称
        List<TeamGroupInfo> teamGroupInfoList = schedulingScheme.getTeamgroupinfo_model();
        if (CollectionUtils.isNotEmpty(teamGroupInfoList) && CollectionUtils.isNotEmpty(dto.getTeamGroupIdList())) {
            //班组名称拼接字符串
            String teamGroupNames = teamGroupInfoList.stream().filter(t -> dto.getTeamGroupIdList().contains(t.getId()))
                    .map(TeamGroupInfo::getName).collect(Collectors.joining(","));
            vo.setTeamGroupNames(teamGroupNames);
        }

        //班次方案，班次名称
        List<ClassesEnergyInfoVO.ClassesName> classesNames = new ArrayList<>();
        for (ClassesScheme classesScheme : schedulingScheme.getClassesscheme_model()) {
            if (CollectionUtils.isEmpty(classesScheme.getClassesconfig_model())) {
                continue;
            }
            List<ClassesConfig> configs = classesScheme.getClassesconfig_model().stream()
                    .filter(c -> dto.getClassesConfigIdList().contains(c.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(configs)) {
                continue;
            }
            ClassesEnergyInfoVO.ClassesName classesName = new ClassesEnergyInfoVO.ClassesName();
            classesName.setSchemeName(classesScheme.getName());
            String configNames = configs.stream().map(ClassesConfig::getName).collect(Collectors.joining(","));
            classesName.setConfigName(configNames);
            classesNames.add(classesName);
        }
        vo.setClassesNames(classesNames);
        return vo;
    }

    /**
     * 根据能源类型和排班方案查询节点树
     *
     * @param energyType         能源类型
     * @param schedulingSchemeId 排班方案id
     * @return 节点树
     */
    @Override
    public List<Map<String, Object>> classesEnergyProjectTree(Integer energyType, Long schedulingSchemeId) {
        //根据能源类型查询节点树
        List<Map<String, Object>> projectManageTreeByEnergyType = eemNodeService.getProjectTree(energyType);
        //查询排班方案关联的节点集合
        List<SchedulingSchemeToNode> schemeToNodes = schemeToNodeDao.queryBySchedulingSchemeId(schedulingSchemeId);
        List<BaseVo> toNodes = schemeToNodes.stream().map(s -> new BaseVo(s.getObjectId(), s.getObjectLabel())).collect(Collectors.toList());
        //遍历节点树，所有的不关联的节点都设置selectchildid = 1 禁止选择！
        return filterAssociationNode(projectManageTreeByEnergyType, toNodes);
    }

    /**
     * 根据排班方案关联节点，过滤节点树
     *
     * @param nodeList 项目节点树
     * @param toNodes  关联节点
     * @return 能源节点树
     */
    private List<Map<String, Object>> filterAssociationNode(List<Map<String, Object>> nodeList, List<BaseVo> toNodes) {
        //遍历节点树
        for (Map<String, Object> node : nodeList) {
            //取出子节点
            List<Map<String, Object>> nodeChildrenList = (List<Map<String, Object>>) node.get(TableColumnNameDef.COLUMN_CHILDREN);

            //当前节点依然有子节点，则先过滤子节点
            if (Objects.nonNull(nodeChildrenList)) {
                nodeChildrenList = filterAssociationNode(nodeChildrenList, toNodes);
                node.put(TableColumnNameDef.COLUMN_CHILDREN, nodeChildrenList);
            }

            Integer id = (Integer) node.get(TableColumnNameDef.COLUMN_ID);
            String modelLabel = (String) node.get(TableColumnNameDef.MODEL_LABEL);

            //如果当前节点没有子节点，也不被tonodes包含，那就删除掉！、
            BaseVo baseVo = new BaseVo(id.longValue(), modelLabel);
            if (toNodes.contains(baseVo)) {
                node.put(ColumnDef.CHILD_SELECT_STATE, 1);
            } else {
                node.put(ColumnDef.CHILD_SELECT_STATE, 2);
            }
        }
        return nodeList.stream()
                .filter(node -> CollectionUtils.isNotEmpty((List<Map<String, Object>>) node.get(TableColumnNameDef.COLUMN_CHILDREN))
                        || Objects.equals(node.get(ColumnDef.CHILD_SELECT_STATE), 1))
                .collect(Collectors.toList());
    }
}