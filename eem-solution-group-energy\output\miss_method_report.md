# 源码上下文分析报告
**生成时间**: 2025-08-27 19:28:15

## 问题 1: queryUserBatch()

```json
{
  "missing_method": "queryUserBatch()",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析 'NodeAuthCheckService' 中的方法 'queryUserBatch'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 2,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamConfigServiceImpl",
    "missing_method": "queryUserBatch()",
    "description": "无法解析 'NodeAuthCheckService' 中的方法 'queryUserBatch'",
    "line": "535"
  }
}
```

## 问题 2: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getTeamgroupinfo_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 3,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getTeamgroupinfo_model()",
    "description": "无法解析方法 'getTeamgroupinfo_model()'",
    "line": "73"
  }
}
```

## 问题 3: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getTeamgroupinfo_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 5,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getTeamgroupinfo_model()",
    "description": "无法解析方法 'getTeamgroupinfo_model()'",
    "line": "78"
  }
}
```

## 问题 4: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getId() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 7,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getId()",
    "description": "无法解析方法 'getId'",
    "line": "79"
  }
}
```

## 问题 5: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getValue() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 11,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getValue()",
    "description": "无法解析方法 'getValue'",
    "line": "89"
  }
}
```

## 问题 6: calcDouble()

```json
{
  "missing_method": "calcDouble()",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析方法 'calcDouble(double, double, int)'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 14,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "calcDouble()",
    "description": "无法解析方法 'calcDouble(double, double, int)'",
    "line": "96"
  }
}
```

## 问题 7: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getTeamGroupId() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 17,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getTeamGroupId()",
    "description": "无法解析方法 'getTeamGroupId'",
    "line": "101"
  }
}
```

## 问题 8: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getId() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 19,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getId()",
    "description": "无法解析方法 'getId()'",
    "line": "110"
  }
}
```

## 问题 9: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getName() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 21,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getName()",
    "description": "无法解析方法 'getName()'",
    "line": "111"
  }
}
```

## 问题 10: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getValue() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 23,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getValue()",
    "description": "无法解析方法 'getValue'",
    "line": "114"
  }
}
```

## 问题 11: calcDouble()

```json
{
  "missing_method": "calcDouble()",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析方法 'calcDouble(double, double, int)'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 25,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "calcDouble()",
    "description": "无法解析方法 'calcDouble(double, double, int)'",
    "line": "118"
  }
}
```

## 问题 12: calcDouble()

```json
{
  "missing_method": "calcDouble()",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析方法 'calcDouble(double, double, int)'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 27,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "calcDouble()",
    "description": "无法解析方法 'calcDouble(double, double, int)'",
    "line": "120"
  }
}
```

## 问题 13: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getUnitEn() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 29,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getUnitEn()",
    "description": "无法解析方法 'getUnitEn()'",
    "line": "141"
  }
}
```

## 问题 14: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesscheme_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 32,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesscheme_model()",
    "description": "无法解析方法 'getClassesscheme_model()'",
    "line": "380"
  }
}
```

## 问题 15: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesscheme_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 33,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesscheme_model()",
    "description": "无法解析方法 'getClassesscheme_model()'",
    "line": "386"
  }
}
```

## 问题 16: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesconfig_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 35,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesconfig_model()",
    "description": "无法解析方法 'getClassesconfig_model()'",
    "line": "387"
  }
}
```

## 问题 17: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesconfig_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 37,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesconfig_model()",
    "description": "无法解析方法 'getClassesconfig_model()'",
    "line": "388"
  }
}
```

## 问题 18: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getValue() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 43,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getValue()",
    "description": "无法解析方法 'getValue'",
    "line": "406"
  }
}
```

## 问题 19: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getValue() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 47,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getValue()",
    "description": "无法解析方法 'getValue'",
    "line": "415"
  }
}
```

## 问题 20: calcDouble()

```json
{
  "missing_method": "calcDouble()",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析方法 'calcDouble(Double, double, int)'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 49,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "calcDouble()",
    "description": "无法解析方法 'calcDouble(Double, double, int)'",
    "line": "417"
  }
}
```

## 问题 21: calcDouble()

```json
{
  "missing_method": "calcDouble()",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析方法 'calcDouble(Double, double, int)'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 51,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "calcDouble()",
    "description": "无法解析方法 'calcDouble(Double, double, int)'",
    "line": "419"
  }
}
```

## 问题 22: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getUnitEn() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 53,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getUnitEn()",
    "description": "无法解析方法 'getUnitEn()'",
    "line": "427"
  }
}
```

## 问题 23: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getTeamgroupinfo_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 55,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getTeamgroupinfo_model()",
    "description": "无法解析方法 'getTeamgroupinfo_model()'",
    "line": "430"
  }
}
```

## 问题 24: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getId() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 57,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getId()",
    "description": "无法解析方法 'getId()'",
    "line": "433"
  }
}
```

## 问题 25: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getName() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 59,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getName()",
    "description": "无法解析方法 'getName'",
    "line": "434"
  }
}
```

## 问题 26: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesscheme_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 62,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesscheme_model()",
    "description": "无法解析方法 'getClassesscheme_model()'",
    "line": "440"
  }
}
```

## 问题 27: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesconfig_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 64,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesconfig_model()",
    "description": "无法解析方法 'getClassesconfig_model()'",
    "line": "441"
  }
}
```

## 问题 28: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesconfig_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 65,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesconfig_model()",
    "description": "无法解析方法 'getClassesconfig_model()'",
    "line": "444"
  }
}
```

## 问题 29: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 stream() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 66,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "stream()",
    "description": "无法解析方法 'stream()'",
    "line": "444"
  }
}
```

## 问题 30: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 filter() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 67,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "filter()",
    "description": "无法解析方法 'filter(<lambda expression>)'",
    "line": "445"
  }
}
```

## 问题 31: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 collect() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 71,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "collect()",
    "description": "无法解析方法 'collect(Collector<T, capture of ?, List<T>>)'",
    "line": "445"
  }
}
```

## 问题 32: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getName() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 73,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getName()",
    "description": "无法解析方法 'getName()'",
    "line": "450"
  }
}
```

## 问题 33: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getName() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 75,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getName()",
    "description": "无法解析方法 'getName'",
    "line": "451"
  }
}
```

## 问题 34: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesId() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 144,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesId()",
    "description": "无法解析方法 'getClassesId'",
    "line": "309"
  }
}
```

## 问题 35: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 anyMatch() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 154,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "anyMatch()",
    "description": "无法解析方法 'anyMatch(<lambda expression>)'",
    "line": "330"
  }
}
```

## 问题 36: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 findFirst() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 159,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "findFirst()",
    "description": "无法解析方法 'findFirst()'",
    "line": "331"
  }
}
```

## 问题 37: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 orElse() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 161,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "orElse()",
    "description": "无法解析方法 'orElse(String)'",
    "line": "331"
  }
}
```

## 问题 38: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getTeamgroupinfo_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 80,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getTeamgroupinfo_model()",
    "description": "无法解析方法 'getTeamgroupinfo_model()'",
    "line": "164"
  }
}
```

## 问题 39: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getTeamgroupinfo_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 82,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getTeamgroupinfo_model()",
    "description": "无法解析方法 'getTeamgroupinfo_model()'",
    "line": "169"
  }
}
```

## 问题 40: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getId() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... # 07 系统事件查询\n\n# SystemEventService 使用文档\n\n## 概述\n\nSystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。\n\n## Maven依赖\n\n在您的项目中添加以下依赖：\n\n```plaintext\n<dependency>\n    <groupId>com.cet.electric</groupId>\n    <artifactId>model-sdk-event</artifactId>\n    <version>1.0.12.16</version>\n</dependency>\n```\n\n## 接口方法详解\n\n### 1. 按条件查询系统事件列表\n\n```plaintext\nListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);\n```\n\n#### 方法说明\n\n根据查询条件查询系统事件列表，支持分页和多种过滤条件。\n\n#### 参...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 84,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getId()",
    "description": "无法解析方法 'getId'",
    "line": "170"
  }
}
```

## 问题 41: queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)

```json
{
  "missing_method": "queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO)",
  "in_param": {
    "dto": "TeamGroupEnergyInfoQueryDTO"
  },
  "out_return": "TeamGroupEnergyInfoVO",
  "context": "方法 getClassesscheme_model() 位于类 TeamEnergyServiceImpl. 知识库信息: # 06 能耗数据查询\n\n## EnergyConsumptionDao（能耗数据查询）\n\n1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；\n    \n2.  方法如下：\n    \n\n1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)\n    \n2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)\n    \n3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)\n ... ## 能管代码迁移知识库\n\n## 1.类问题\n\n### 1. Controller 层变更\n\n```\ncontroller_changes:\n  url_prefix:\n    description: \"Controller层接口URL前缀变更\"\n    detection_pattern: \"@RequestMapping|@GetMapping|@PostMapping\"\n    rules:\n      - old_pattern: \"@RequestMapping(\\\"/api/v1/\"\n        new_pattern: \"@RequestMapping(\\\"/api/v2/plugin/{plugin-name}/\"\n        example:\n          before: '@RequestMapping(\"/api/v1/energy\")'\n          after: '@RequestMapping(\"/api/v2/plugin/energy-management/\")'\n\n  return_type:\n    description: \"...",
  "content": "    /**\n     * 查询班组能耗数据\n     *\n     * @param dto 查询条件\n     * @return 班组能耗数据\n     */\n    @Override\n    public TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto) {\n        //初始化返回数据\n        TeamGroupEnergyInfoVO vo = new TeamGroupEnergyInfoVO();\n\n        //查询排班方案\n        SchedulingScheme schedulingScheme = schedulingSchemeDao.queryAssociationNodeById(dto.getSchedulingSchemeId());\n        if (Objects.isNull(schedulingScheme) || CollectionUtils.isEmpty(schedulingScheme.getTeamgroupinfo_model())) {\n            return vo;\n        }\n\n        //取出排版方案之下班组\n        List<TeamGroupInfo> teamGroups = schedulingScheme.getTeamgroupinfo_model();\n        List<Long> teamGroupIds = teamGroups.stream().map(TeamGroupInfo::getId).collect(Collectors.toList());\n\n        //查询班组能耗\n        List<TeamGroupEnergy> teamGroupEnergyList = teamGroupEnergyDao.queryTeamGroupEnergy(dto.getStartTime(), dto.getEndTime(),\n                dto.getEnergyType(), dto.getNodeId(), dto.getNodeLabel(), teamGroupIds, AggregationCycle.ONE_DAY);\n        if (CollectionUtils.isEmpty(teamGroupEnergyList)) {\n            return vo;\n        }\n\n        //总能耗\n        double energyTotal = teamGroupEnergyList.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n        UserDefineUnit unit = unitService.getUnit(Collections.singletonList(energyTotal), ProjectUnitClassify.ENERGY, dto.getEnergyType());\n\n        //总班次\n        Integer size = teamGroupEnergyList.size();\n\n        //平均班次能耗\n        Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());\n\n\n        //分班组统计能耗数据\n        Map<Long, List<TeamGroupEnergy>> teamGroupEnergyGroup = teamGroupEnergyList.stream()\n                .collect(Collectors.groupingBy(TeamGroupEnergy::getTeamGroupId));\n\n        List<TeamGroupEnergyCard> teamGroupEnergyCardList = new ArrayList<>();\n        for (Map.Entry<Long, List<TeamGroupEnergy>> teamGroupEnergyEntity : teamGroupEnergyGroup.entrySet()) {\n            TeamGroupEnergyCard card = new TeamGroupEnergyCard();\n\n            Long teamGroupId = teamGroupEnergyEntity.getKey();\n            List<TeamGroupEnergy> teamGroupEnergies = teamGroupEnergyEntity.getValue();\n\n            Optional<TeamGroupInfo> first = teamGroups.stream().filter(t -> Objects.equals(t.getId(), teamGroupId)).findFirst();\n            first.ifPresent(teamGroupInfo -> card.setTeamGroupName(teamGroupInfo.getName()));\n\n            //班组总用能\n            double energyCount = teamGroupEnergies.stream().mapToDouble(TeamGroupEnergy::getValue).sum();\n            //班组总班次\n            Integer classesTotal = teamGroupEnergies.size();\n            //班组平均班次用能\n            Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());\n            //用能占比\n            Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());\n\n            card.setTeamGroupId(teamGroupId);\n            card.setClassesTotal(classesTotal);\n            card.setAvgEnergy(unitConversion(teamAvgEnergy, unit));\n            card.setEnergyCount(DoubleUtils.round(unitConversion(energyCount, unit), 2));\n            card.setEnergyProportion(DoubleUtils.round(energyProportion * 100, 1));\n\n            teamGroupEnergyCardList.add(card);\n        }\n\n        //按照平均班次能耗升序排序后，再保留两位小数\n        teamGroupEnergyCardList = teamGroupEnergyCardList.stream()\n                .sorted(Comparator.comparing(TeamGroupEnergyCard::getAvgEnergy)\n                        .thenComparing(TeamGroupEnergyCard::getTeamGroupId))\n                .peek(card -> card.setAvgEnergy(DoubleUtils.round(card.getAvgEnergy(), 2)))\n                .collect(Collectors.toList());\n\n        vo.setAvgEnergy(DoubleUtils.round(unitConversion(avgEnergy, unit), 2));\n        vo.setEnergyTotal(DoubleUtils.round(unitConversion(energyTotal, unit), 2));\n        vo.setClassesTotal(size);\n        vo.setEnergyUnit(unit.getUnitEn());\n        vo.setTeamGroupEnergyCardList(teamGroupEnergyCardList);\n\n\n        return vo;\n    }",
  "notes": "",
  "original_issue": {
    "issue_id": 87,
    "error_code": "miss_method",
    "module": "eem-solution-group-energy-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getClassesscheme_model()",
    "description": "无法解析方法 'getClassesscheme_model()'",
    "line": "173"
  }
}
```
