[{"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.config", "class": "PluginConfiguration", "missing_method": "(())", "description": "'com.cet.electric.fusion.matrix.v2.dto.business.PluginInfo' 中的 'setPluginname(java.lang.String)' 无法应用于 '(?)'", "line": "20", "ai_analysis": "这个错误表明在调用 PluginInfo 对象的 setPluginname 方法时，传入的参数类型不匹配。错误信息显示期望的是 java.lang.String 类型参数，但实际传入的参数类型无法确定（显示为 ?）。这通常发生在：1. 传入了 null 值但方法不接受 null；2. 传入了错误的数据类型；3. 变量类型推断失败。需要检查调用处传入的参数是否为正确的 String 类型。", "classification_reason": "错误明确指出方法参数类型不匹配，setPluginname 方法存在但参数类型错误，属于典型的 wrong_params 分类"}, {"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.dao.impl", "class": "ClassesSchemeDaoImpl", "missing_method": "(())", "description": "'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'", "line": "35", "ai_analysis": "这个错误发生在使用 ParentQueryConditionBuilder.of() 静态方法时，该方法期望接收一个 String 类型参数，但实际传入的参数类型不匹配。ParentQueryConditionBuilder 是一个查询条件构建器工具类，of() 方法通常用于创建查询条件实例。错误原因可能是传入了错误的数据类型或 null 值。需要确保传入正确的 String 类型参数。", "classification_reason": "方法签名明确，of() 方法存在但参数类型不匹配，属于参数错误，分类为 wrong_params"}, {"issue_id": 1, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryAssociationNodeById()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'", "line": "72", "ai_analysis": "这是一个典型的导入问题。错误显示实际类型是完整包名的 SchedulingScheme（com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme），但期望的是简单类名 SchedulingScheme。这通常表示缺少正确的 import 语句，或者存在类名冲突。解决方案是添加正确的 import 语句：import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;", "classification_reason": "类型不匹配但方法存在，这是导入或类型转换问题，属于 wrong_params 分类"}, {"issue_id": 2, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection<?>)' 无法应用于 '(?)'", "line": "73", "ai_analysis": "CollectionUtils.isEmpty() 方法期望接收一个 Collection 类型的参数，但传入的参数类型无法确定（显示为 ?）。这通常发生在：1. 传入了 null 值；2. 传入了非 Collection 类型的对象；3. 变量类型推断失败。CollectionUtils.isEmpty() 是一个常用的工具方法，用于检查集合是否为空或 null。需要确保传入的参数是 Collection 类型。", "classification_reason": "isEmpty 方法存在但参数类型不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 4, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getTeamgroupinfo_model()", "description": "不兼容的类型。实际为 null'，需要 'java.util.List<TeamGroupInfo>'", "line": "78", "ai_analysis": "代码中某个位置返回了 null 值，但期望的是 List<TeamGroupInfo> 类型。这通常发生在方法调用返回 null，但调用方期望得到具体的列表对象。可能的原因：1. getTeamgroupinfo_model() 方法返回 null；2. 条件判断导致某些分支返回 null；3. 初始化失败。需要添加 null 检查或确保方法返回空列表而不是 null。", "classification_reason": "类型不匹配错误，期望 List 但得到 null，属于类型/参数问题，分类为 wrong_params"}, {"issue_id": 6, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function<? super TeamGroupInfo,?>)' 无法应用于 '(<method reference>)'", "line": "79", "ai_analysis": "Stream.map() 方法的方法引用参数类型不匹配。错误表明传入的方法引用无法转换为期望的 Function 类型。这通常发生在：1. 方法引用指向的方法不存在；2. 方法签名不匹配；3. 泛型类型推断失败。需要检查方法引用是否正确，以及被引用的方法是否存在和可访问。", "classification_reason": "map 方法存在但方法引用参数不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 8, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List<java.lang.Object>'，需要 'java.util.List<java.lang.Long>'", "line": "79", "ai_analysis": "Stream.collect() 操作的结果类型不匹配。期望得到 List<Long>，但实际得到 List<Object>。这通常是由于上游的 map 操作返回了 Object 类型而不是 Long 类型。这与 issue_id 6 和 7 相关，都是同一个流操作链的问题。需要确保 map 操作中的方法引用返回正确的 Long 类型。", "classification_reason": "collect 方法存在但返回类型不匹配，属于类型转换问题，分类为 wrong_params"}, {"issue_id": 9, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryTeamGroupEnergy()", "description": "不兼容的类型。实际为 java.util.List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>'，需要 'java.util.List<TeamGroupEnergy>'", "line": "82", "ai_analysis": "又一个导入问题。实际类型是完整包名的 TeamGroupEnergy，但期望的是简单类名。这与 issue_id 1 的 SchedulingScheme 问题类似。需要添加正确的 import 语句：import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;", "classification_reason": "类型不匹配但类存在，这是导入问题，属于 wrong_params 分类"}, {"issue_id": 10, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'", "line": "89", "ai_analysis": "Stream.mapToDouble() 方法的方法引用参数类型不匹配。该方法期望一个 ToDoubleFunction，但传入的方法引用无法转换为该类型。这通常是因为被引用的方法不存在或返回类型不是 double。结合上下文，这可能与 getValue() 方法缺失有关（issue_id 11）。", "classification_reason": "mapToDouble 方法存在但方法引用参数不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 12, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryUnitCoef()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'", "line": "90", "ai_analysis": "又一个导入问题。实际类型是完整包名的 UserDefineUnitDTO，但期望的是简单类名。需要添加正确的 import 语句：import com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO;", "classification_reason": "类型不匹配但类存在，这是导入问题，属于 wrong_params 分类"}, {"issue_id": 13, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "96", "ai_analysis": "calcDouble() 方法调用返回了 null，但期望的是 Double 类型。这通常发生在：1. calcDouble 方法实现返回 null；2. 条件判断导致某些分支返回 null；3. 计算过程中出现异常返回 null。虽然 calcDouble 方法本身不存在（见 issue_id 14），但这里的错误是关于返回值类型的。", "classification_reason": "返回值类型不匹配，期望 Double 但得到 null，属于类型问题，分类为 wrong_params"}, {"issue_id": 15, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.Map<java.lang.Object,java.util.List<TeamGroupEnergy>>'，需要 'java.util.Map<java.lang.Long,java.util.List<TeamGroupEnergy>>'", "line": "101", "ai_analysis": "Stream.collect() 操作的结果类型不匹配。期望得到 Map<Long, List<TeamGroupEnergy>>，但实际得到 Map<Object, List<TeamGroupEnergy>>。这通常是由于 groupingBy 操作中的分组键类型不正确。与 getTeamGroupId() 方法缺失相关（issue_id 17），导致分组键类型推断为 Object。", "classification_reason": "collect 方法存在但返回的 Map 键类型不匹配，属于类型转换问题，分类为 wrong_params"}, {"issue_id": 16, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function<? super java.lang.Object,?>)' 无法应用于 '(<method reference>)'", "line": "101", "ai_analysis": "Collectors.groupingBy() 方法的方法引用参数类型不匹配。这与 issue_id 15 和 17 相关，都是同一个流操作的问题。由于 getTeamGroupId() 方法不存在，导致方法引用无法解析，进而影响 groupingBy 的类型推断。", "classification_reason": "groupingBy 方法存在但方法引用参数不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 18, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "t()", "description": "'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'", "line": "110", "ai_analysis": "Objects.equals() 方法的第一个参数类型无法确定（显示为 ?）。这通常是由于第一个参数的方法调用失败导致类型推断失败。结合上下文，这可能与 getId() 方法缺失有关（issue_id 19），导致第一个参数无法正确获取。", "classification_reason": "equals 方法存在但第一个参数类型无法确定，属于参数类型问题，分类为 wrong_params"}, {"issue_id": 20, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'", "line": "111", "ai_analysis": "TeamGroupEnergyCard.setTeamGroupName() 方法的参数类型不匹配。该方法期望 String 类型参数，但传入的参数类型无法确定。这可能与 getName() 方法缺失有关（issue_id 21），导致无法获取正确的名称字符串。", "classification_reason": "setTeamGroupName 方法存在但参数类型不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 22, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'", "line": "114", "ai_analysis": "又一个 Stream.mapToDouble() 方法的方法引用参数类型不匹配错误。与 issue_id 10 类似，这也是由于 getValue() 方法缺失导致的方法引用无法解析。", "classification_reason": "mapToDouble 方法存在但方法引用参数不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 24, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "118", "ai_analysis": "又一个 calcDouble() 方法返回 null 但期望 Double 类型的错误。与 issue_id 13 类似，这是返回值类型不匹配的问题。", "classification_reason": "返回值类型不匹配，期望 Double 但得到 null，属于类型问题，分类为 wrong_params"}, {"issue_id": 26, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "120", "ai_analysis": "第三个 calcDouble() 方法返回 null 但期望 Double 类型的错误。这进一步确认了计算方法返回值的问题。", "classification_reason": "返回值类型不匹配，期望 Double 但得到 null，属于类型问题，分类为 wrong_params"}, {"issue_id": 28, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'", "line": "141", "ai_analysis": "TeamGroupEnergyInfoVO.setEnergyUnit() 方法的参数类型不匹配。该方法期望 String 类型参数，但传入的参数类型无法确定。这可能与 getUnitEn() 方法缺失有关（issue_id 29）。", "classification_reason": "setEnergyUnit 方法存在但参数类型不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 30, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryAssociationNodeById()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'", "line": "379", "ai_analysis": "又一个 SchedulingScheme 导入问题，与 issue_id 1 相同。这表明在多个地方都存在相同的导入问题。", "classification_reason": "类型不匹配但类存在，这是导入问题，属于 wrong_params 分类"}, {"issue_id": 31, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection<?>)' 无法应用于 '(?)'", "line": "380", "ai_analysis": "又一个 CollectionUtils.isEmpty() 方法的参数类型不匹配错误，与 issue_id 2 相同。这表明在多个地方都存在相同的集合检查问题。", "classification_reason": "isEmpty 方法存在但参数类型不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 34, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection<?>)' 无法应用于 '(?)'", "line": "387", "ai_analysis": "CollectionUtils.isNotEmpty() 方法的参数类型不匹配。该方法期望 Collection 类型参数，但传入的参数类型无法确定。这与 isEmpty() 方法的问题类似，都是集合工具类的参数类型问题。", "classification_reason": "isNotEmpty 方法存在但参数类型不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 36, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.List' 中的 'addAll(java.util.Collection<? extends ClassesConfig>)' 无法应用于 '(?)'", "line": "388", "ai_analysis": "List.addAll() 方法的参数类型不匹配。该方法期望 Collection<? extends ClassesConfig> 类型参数，但传入的参数类型无法确定。这可能与 getClassesconfig_model() 方法缺失有关（issue_id 37）。", "classification_reason": "addAll 方法存在但参数类型不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 38, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.stream.Stream' 中的 'map(java.util.function.Function<? super ClassesConfig,?>)' 无法应用于 '(<method reference>)'", "line": "395", "ai_analysis": "Stream.map() 方法的方法引用参数类型不匹配。这与 getId() 方法缺失有关（issue_id 39），导致方法引用无法解析。", "classification_reason": "map 方法存在但方法引用参数不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 40, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "collect()", "description": "不兼容的类型。实际为 java.util.List<java.lang.Object>'，需要 'java.util.List<java.lang.Long>'", "line": "396", "ai_analysis": "Stream.collect() 操作的结果类型不匹配。期望得到 List<Long>，但实际得到 List<Object>。这与上游的 map 操作和 getId() 方法缺失相关。", "classification_reason": "collect 方法存在但返回类型不匹配，属于类型转换问题，分类为 wrong_params"}, {"issue_id": 41, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryClassesConfigDayEnergy()", "description": "不兼容的类型。实际为 java.util.List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>'，需要 'java.util.List<TeamGroupEnergy>'", "line": "399", "ai_analysis": "又一个 TeamGroupEnergy 导入问题，与 issue_id 9 相同。这表明在多个地方都存在相同的导入问题。", "classification_reason": "类型不匹配但类存在，这是导入问题，属于 wrong_params 分类"}, {"issue_id": 42, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'", "line": "406", "ai_analysis": "又一个 Stream.mapToDouble() 方法的方法引用参数类型不匹配错误。这与 getValue() 方法缺失有关（issue_id 43）。", "classification_reason": "mapToDouble 方法存在但方法引用参数不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 44, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryUnitCoef()", "description": "不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'", "line": "407", "ai_analysis": "又一个 UserDefineUnitDTO 导入问题，与 issue_id 12 相同。这表明在多个地方都存在相同的导入问题。", "classification_reason": "类型不匹配但类存在，这是导入问题，属于 wrong_params 分类"}, {"issue_id": 45, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "queryClassesConfigDayEnergy()", "description": "不兼容的类型。实际为 java.util.List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>'，需要 'java.util.List<TeamGroupEnergy>'", "line": "410", "ai_analysis": "又一个 TeamGroupEnergy 导入问题，与之前的错误相同。这是一个重复的导入问题。", "classification_reason": "类型不匹配但类存在，这是导入问题，属于 wrong_params 分类"}, {"issue_id": 46, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'", "line": "415", "ai_analysis": "又一个 Stream.mapToDouble() 方法的方法引用参数类型不匹配错误，与之前的错误模式相同。", "classification_reason": "mapToDouble 方法存在但方法引用参数不匹配，属于参数类型错误，分类为 wrong_params"}, {"issue_id": 48, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "417", "ai_analysis": "又一个 calcDouble() 方法返回 null 但期望 Double 类型的错误，与之前的错误模式相同。", "classification_reason": "返回值类型不匹配，期望 Double 但得到 null，属于类型问题，分类为 wrong_params"}, {"issue_id": 50, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "calcDouble()", "description": "不兼容的类型。实际为 null'，需要 'java.lang.Double'", "line": "419", "ai_analysis": "又一个 calcDouble() 方法返回 null 但期望 Double 类型的错误。", "classification_reason": "返回值类型不匹配，期望 Double 但得到 null，属于类型问题，分类为 wrong_params"}, {"issue_id": 52, "error_code": "wrong_params", "module": "eem-solution-group-energy-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "(())", "description": "'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'", "line": "427", "ai_analysis": "ClassesEnergyInfoVO.setEnergyUnit() 方法的参数类型不匹配，与之前的 setEnergyUnit 错误模式相同。", "classification_reason": "setEnergyUnit 方法存在但参数类型不匹配，属于参数类型错误，分类为 wrong_params"}]