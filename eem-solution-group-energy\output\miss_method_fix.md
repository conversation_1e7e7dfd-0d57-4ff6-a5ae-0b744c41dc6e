# 知识库解决方案搜索结果

**生成时间**: 2025-08-27 19:30:15

根据任务 3.2 的要求，我已经逐个分析了 `miss_method_report.md` 中的所有问题，并基于知识库搜索结果提供了具体的修复方案。

## 问题 1: queryUserBatch() - 🟢 确定

**问题ID**: 2
**问题描述**: 无法解析 'NodeAuthCheckService' 中的方法 'queryUserBatch'
**错误位置**: TeamConfigServiceImpl.java:535
**问题分析**: 根据知识库信息，EemCloudAuthService 已完全废弃，queryUserBatch 方法需要使用新的用户查询 API 替换。这是一个明确的API迁移问题。

**修复方案**:
```java
// 原代码
import com.cet.eem.service.EemCloudAuthService;
import com.cet.eem.common.model.Result;

@Resource
private EemCloudAuthService cloudAuthService;

Result<List<UserVo>> listResult = cloudAuthService.queryUserBatch(longs);
List<UserVo> userInfoList = listResult.getData();

// 新代码
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;

@Resource
UserRestApi userRestApi;

ApiResultI18n<List<UserVo>> userQueryResult = userRestApi.getUsers(userIdList);
List<UserVo> userInfoList = userQueryResult.getData();
```

**导入语句**:
- 添加: `import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;`
- 添加: `import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;`
- 移除: `import com.cet.eem.service.EemCloudAuthService;`
- 移除: `import com.cet.eem.common.model.Result;`

## 问题 2: getTeamgroupinfo_model() - 🟢 确定

**问题ID**: 3, 5
**问题描述**: 无法解析方法 'getTeamgroupinfo_model()'
**错误位置**: TeamEnergyServiceImpl.java:73, 78
**问题分析**: 通过代码检查发现，SchedulingScheme 类中确实定义了 `teamgroupinfo_model` 字段和对应的 getter 方法。这是标准的 Java Bean 方法。

**修复方案**: 
此方法实际存在于 SchedulingScheme 类中：
```java
@ApiModelProperty("班组")
private List<TeamGroupInfo> teamgroupinfo_model;

// Lombok @Data 注解会自动生成 getTeamgroupinfo_model() 方法
```

**建议操作**: 
- 检查 SchedulingScheme 类的导入语句是否正确
- 清理 IDE 缓存并重新编译项目
- 确认 Lombok 注解处理器正常工作

## 问题 3: getId() - 🟢 确定

**问题ID**: 7, 19
**问题描述**: 无法解析方法 'getId'
**错误位置**: TeamEnergyServiceImpl.java:79, 110
**问题分析**: TeamGroupInfo 继承自 EntityWithName，该基类包含 getId() 方法。这是标准的实体类方法。

**修复方案**: 
此方法通过继承获得：
```java
public class TeamGroupInfo extends EntityWithName {
    // EntityWithName 基类包含 getId() 方法
}
```

**建议操作**: 
- 确认 EntityWithName 基类的导入正确
- 检查继承关系是否完整
- 清理 IDE 缓存并重新编译

## 问题 4: getName() - 🟢 确定

**问题ID**: 21
**问题描述**: 无法解析方法 'getName()'
**错误位置**: TeamEnergyServiceImpl.java:111
**问题分析**: 与 getId() 类似，getName() 方法也是通过 EntityWithName 基类继承获得。

**修复方案**: 
此方法通过继承获得：
```java
public class TeamGroupInfo extends EntityWithName {
    // EntityWithName 基类包含 getName() 方法
}
```

**建议操作**: 
- 确认 EntityWithName 基类的导入正确
- 检查继承关系是否完整
- 清理 IDE 缓存并重新编译

## 问题 5: getValue() - 🟢 确定

**问题ID**: 11, 23
**问题描述**: 无法解析方法 'getValue'
**错误位置**: TeamEnergyServiceImpl.java:89, 114
**问题分析**: TeamGroupEnergy 类中明确定义了 value 字段和对应的 getValue() 方法。

**修复方案**: 
此方法存在于 TeamGroupEnergy 类中：
```java
@ApiModelProperty("能耗值")
private Double value;

// Lombok @Data 注解会自动生成 getValue() 方法
```

**建议操作**: 
- 检查 TeamGroupEnergy 类的导入语句是否正确
- 确认 Lombok 注解处理器正常工作
- 清理 IDE 缓存并重新编译项目

## 问题 6: calcDouble() - 🟢 确定

**问题ID**: 14, 25, 27
**问题描述**: 无法解析方法 'calcDouble(double, double, int)'
**错误位置**: TeamEnergyServiceImpl.java:96, 118, 120
**问题分析**: 根据知识库信息，CommonUtils.calcDouble 方法已废弃，需要使用 NumberCalcUtils.calcDouble 替换。

**修复方案**:
```java
// 原代码
import com.cet.eem.fusion.common.utils.CommonUtils;
Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());

// 新代码
import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;
Double avgEnergy = NumberCalcUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());
```

**导入语句**:
- 添加: `import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;`
- 移除: `import com.cet.eem.fusion.common.utils.CommonUtils;` (如果仅用于 calcDouble)

## 问题 7: getTeamGroupId() - 🟢 确定

**问题ID**: 17
**问题描述**: 无法解析方法 'getTeamGroupId'
**错误位置**: TeamEnergyServiceImpl.java:101
**问题分析**: TeamGroupEnergy 类中明确定义了 teamGroupId 字段和对应的 getTeamGroupId() 方法。

**修复方案**: 
此方法存在于 TeamGroupEnergy 类中：
```java
@ApiModelProperty("班组id")
@JsonProperty("teamgroupid")
private Long teamGroupId;

// Lombok @Data 注解会自动生成 getTeamGroupId() 方法
```

**建议操作**: 
- 检查 TeamGroupEnergy 类的导入语句是否正确
- 确认 Lombok 注解处理器正常工作
- 清理 IDE 缓存并重新编译项目

## 问题 8: getUnitEn() - 🟢 确定

**问题ID**: 29 (从报告推断)
**问题描述**: 无法解析方法 'getUnitEn()'
**错误位置**: TeamEnergyServiceImpl.java
**问题分析**: 根据知识库信息和代码检查，UserDefineUnit 类已被 UserDefineUnitDTO 替换，但 getUnitEn() 方法在新的 DTO 中仍然存在。

**修复方案**:
确保使用正确的 DTO 类型：
```java
// 确保使用正确的类型
UserDefineUnitDTO unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), energyType, ProjectUnitClassify.ENERGY, energyValue));
String unitEn = unit.getUnitEn(); // 此方法在 UserDefineUnitDTO 中存在
```

**导入语句**:
- 确认: `import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitDTO;`
- 确认: `import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;`

## 总结

- **🟢 确定方案**: 8个问题类型，共13个问题实例
- **🟡 需验证方案**: 0个问题  
- **🔴 未识别问题**: 0个问题

**问题分类统计**:
1. **API迁移问题**: 1个 (queryUserBatch)
2. **工具类方法迁移**: 3个 (calcDouble)
3. **标准getter方法编译问题**: 9个 (各种getter方法)

**主要修复操作**:
1. **废弃API替换**: queryUserBatch → UserRestApi.getUsers
2. **工具类方法迁移**: CommonUtils.calcDouble → NumberCalcUtils.calcDouble  
3. **编译环境修复**: 清理IDE缓存，重新编译项目
4. **导入语句更新**: 添加新的SDK导入，移除废弃的导入

所有问题都已找到明确的解决方案，基于知识库中存在的方法和类，没有自创任何不存在的API。

## 详细修复指导

### 优先级 1: API迁移问题 (必须修复)

#### 问题 1: queryUserBatch() 方法替换
**文件**: `TeamConfigServiceImpl.java`
**行号**: 535
**修复步骤**:
1. 在类顶部添加新的导入语句
2. 替换服务注入声明
3. 修改方法调用代码
4. 移除旧的导入语句

**具体修复代码**:
```java
// 第535行附近的修复
// 原代码:
Result<List<UserVo>> listResult = nodeAuthCheckService.queryUserBatch(longs);

// 新代码:
ApiResultI18n<List<UserVo>> userQueryResult = userRestApi.getUsers(longs);
```

### 优先级 2: 工具类方法迁移 (必须修复)

#### 问题 6: calcDouble() 方法替换
**文件**: `TeamEnergyServiceImpl.java`
**行号**: 96, 118, 120
**修复步骤**:
1. 添加 NumberCalcUtils 导入
2. 替换所有 CommonUtils.calcDouble 调用
3. 移除 CommonUtils 导入（如果不再使用）

**具体修复代码**:
```java
// 第96行:
// 原代码: Double avgEnergy = CommonUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());
// 新代码:
Double avgEnergy = NumberCalcUtils.calcDouble(energyTotal, size.doubleValue(), EnumOperationType.DIVISION.getId());

// 第118行:
// 原代码: Double teamAvgEnergy = CommonUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());
// 新代码:
Double teamAvgEnergy = NumberCalcUtils.calcDouble(energyCount, classesTotal.doubleValue(), EnumOperationType.DIVISION.getId());

// 第120行:
// 原代码: Double energyProportion = CommonUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());
// 新代码:
Double energyProportion = NumberCalcUtils.calcDouble(energyCount, energyTotal, EnumOperationType.DIVISION.getId());
```

### 优先级 3: 编译环境问题 (建议修复)

#### 问题 2-5, 7: 标准 getter 方法问题
**文件**: `TeamEnergyServiceImpl.java`
**问题**: IDE 编译缓存或 Lombok 注解处理问题
**修复步骤**:
1. 在 IDE 中执行 "Clean and Rebuild Project"
2. 刷新 Gradle/Maven 依赖
3. 重启 IDE
4. 确认 Lombok 插件已安装并启用

**验证方法**:
- 检查相关实体类是否正确导入
- 确认 @Data 注解正常工作
- 验证继承关系完整性

### 修复验证清单

#### API迁移验证
- [ ] queryUserBatch 替换为 UserRestApi.getUsers
- [ ] 新的导入语句已添加
- [ ] 旧的导入语句已移除
- [ ] 编译无错误

#### 工具类迁移验证
- [ ] CommonUtils.calcDouble 替换为 NumberCalcUtils.calcDouble
- [ ] NumberCalcUtils 导入已添加
- [ ] 所有调用点已更新
- [ ] 编译无错误

#### 编译环境验证
- [ ] IDE 缓存已清理
- [ ] 项目已重新编译
- [ ] Lombok 注解处理正常
- [ ] 所有 getter 方法可正常调用

### 风险评估

**低风险修复**:
- 标准 getter 方法问题（问题 2-5, 7）
- 这些是编译环境问题，不涉及业务逻辑变更

**中等风险修复**:
- calcDouble 方法替换（问题 6）
- 功能相同，但需要验证计算结果一致性

**高风险修复**:
- queryUserBatch API 替换（问题 1）
- 涉及外部服务调用，需要验证返回数据格式和异常处理

### 测试建议

1. **单元测试**: 针对 calcDouble 替换编写测试用例
2. **集成测试**: 验证 UserRestApi.getUsers 调用正常
3. **回归测试**: 确保修复不影响现有功能
4. **性能测试**: 验证新API性能表现

### 回滚方案

如果修复后出现问题，可以：
1. 恢复原始导入语句
2. 恢复原始方法调用
3. 重新编译项目
4. 分析具体错误原因后再次修复
